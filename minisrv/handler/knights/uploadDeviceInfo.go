package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/user"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 上传设备信息
func (_ KnightsHandlers) UploadDeviceInfoHandler(ctx *wctx.Context, req *wrpc.UploadDeviceInfoRequest) (*wrpc.UploadDeviceInfoReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		if req.UID == 16386 {
			//util.SendFeishuText("https://open.feishu.cn/open-apis/bot/v2/hook/fd0ba3bc-d9f9-4ca9-806e-a34667973c04", "ip 触发限制: "+ip+" country: "+country)
			err = kdmerr.IpIsInLimit.CastErrorf("ip is not allowed")
		}
		if req.DeviceInfo != "" {
			err = user.SetUserDevice(ctx, req.UID, req.DeviceInfo)
		}
		replay := wrpc.UploadDeviceInfoReply{
			Result: err == nil,
		}
		rawResp = &replay

	})
	return rawResp.(*wrpc.UploadDeviceInfoReply), err
}
