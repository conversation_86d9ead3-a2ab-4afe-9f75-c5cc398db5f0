package knights

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/iap"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc/wctx"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

// 一键阅读并领取邮件
// 领取成长基金奖励
func (_ KnightsHandlers) CollectGrowthFundRewardHandler(ctx *wctx.Context, req *wrpc.CollectGrowthFundRewardRequest) (*wrpc.CollectGrowthFundRewardReply, error) {
	var rawResp interface{}
	var err error
	tasklet.Invoke(ctx, req.String(), func(ctx context.Context) {
		ret, e := iap.GetGrowthFundReward(ctx, req.UID, req.FundId)
		rawResp = &wrpc.CollectGrowthFundRewardReply{Result: ret}
		err = e
	})
	if rawResp != nil {
		return rawResp.(*wrpc.CollectGrowthFundRewardReply), err
	}
	return nil, nil
}
