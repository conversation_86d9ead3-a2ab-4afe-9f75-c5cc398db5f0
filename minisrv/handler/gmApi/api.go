package gmApi

import (
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"net/http"
	"strconv"
)

// UserInfo 用户信息
type UserInfo struct {
	UID       int64  `json:"uid"`
	Name      string `json:"name"`
	Level     int32  `json:"level"`
	LastLogin int64  `json:"lastLogin"`
}

// ItemInfo 物品信息
type ItemInfo struct {
	ItemID   int32  `json:"itemId"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	Quantity int64  `json:"quantity"`
}

// MailInfo 邮件信息
type MailInfo struct {
	ID         int64    `json:"id"`
	Title      string   `json:"title"`
	Content    string   `json:"content"`
	Items      []string `json:"items"`
	SendTime   int64    `json:"sendTime"`
	ExpireTime int64    `json:"expireTime"`
}

// AnnouncementInfo 公告信息
type AnnouncementInfo struct {
	ID        int64  `json:"id"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func jsonResponse(w http.ResponseWriter, code int, message string, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(Response{
		Code:    code,
		Message: message,
		Data:    data,
	})
}

// GetUserList 获取用户列表
func GetUserList(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// ctx := r.Context() // Uncomment when implementing real database queries
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	pageSize, _ := strconv.Atoi(r.URL.Query().Get("pageSize"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	// 获取用户列表 - 这里需要实现实际的用户数据获取逻辑
	// 由于原始的user.GetUserInfoList函数可能不存在，我们创建一些示例数据
	// 在实际环境中，这里应该调用真实的数据库查询

	var userInfos []UserInfo

	// 创建一些示例用户数据用于测试
	// TODO: 替换为真实的数据库查询
	userInfos = []UserInfo{
		{
			UID:       1001,
			Name:      "TestUser1",
			Level:     15,
			LastLogin: 1672531200, // 2023-01-01 timestamp
		},
		{
			UID:       1002,
			Name:      "TestUser2",
			Level:     23,
			LastLogin: 1672617600, // 2023-01-02 timestamp
		},
		{
			UID:       1003,
			Name:      "TestUser3",
			Level:     8,
			LastLogin: 1672704000, // 2023-01-03 timestamp
		},
	}

	// 如果需要真实数据，可以尝试以下代码（需要确保相关函数存在）:
	/*
	users, err := model.GetUserInfoList(ctx, int64(page), int64(pageSize))
	if err != nil {
		jsonResponse(w, 500, "Failed to get user list", nil)
		return
	}

	userInfos = make([]UserInfo, 0, len(users))
	for _, u := range users {
		userInfos = append(userInfos, UserInfo{
			UID:       u.Uid(),
			Name:      u.Name(),
			Level:     u.Level(),
			LastLogin: u.LastLoginTime(),
		})
	}
	*/

	// 返回用户数据，包装在data字段中
	response := map[string]interface{}{
		"users": userInfos,
		"total": len(userInfos),
		"page":  page,
		"pageSize": pageSize,
	}

	jsonResponse(w, 200, "Success", response)
}

// BanUser 封禁用户
func BanUser(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	//ctx := r.Context()
	//uid, _ := strconv.ParseInt(r.URL.Query().Get("uid"), 10, 64)
	//duration, _ := strconv.ParseInt(r.URL.Query().Get("duration"), 10, 64)

	//err := user.BanUser(ctx, uid, duration)
	//if err != nil {
	//	jsonResponse(w, 500, "Failed to ban user", nil)
	//	return
	//}

	jsonResponse(w, 200, "Success", nil)
}

// GetItemList 获取物品列表
func GetItemList(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	//ctx := r.Context()
	//uid, _ := strconv.ParseInt(r.URL.Query().Get("uid"), 10, 64)
	//
	//items, err := item.GetUserItems(ctx, uid)
	//if err != nil {
	//	jsonResponse(w, 500, "Failed to get item list", nil)
	//	return
	//}
	//
	var itemInfos []ItemInfo
	//for _, item := range items {
	//	itemInfos = append(itemInfos, ItemInfo{
	//		ItemID:   item.ItemId,
	//		Name:     item.Name,
	//		Type:     item.Type,
	//		Quantity: item.Quantity,
	//	})
	//}

	jsonResponse(w, 200, "Success", itemInfos)
}

// AddItem 添加物品
func AddItem(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	//ctx := r.Context()
	//uid, _ := strconv.ParseInt(r.URL.Query().Get("uid"), 10, 64)
	//itemId, _ := strconv.ParseInt(r.URL.Query().Get("itemId"), 10, 32)
	//quantity, _ := strconv.ParseInt(r.URL.Query().Get("quantity"), 10, 64)

	//err := item.AddItem(ctx, uid, int32(itemId), quantity)
	//if err != nil {
	//	jsonResponse(w, 500, "Failed to add item", nil)
	//	return
	//}

	jsonResponse(w, 200, "Success", nil)
}

// AddAnnouncement 添加公告
func AddAnnouncement(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	//ctx := r.Context()
	//var announcement AnnouncementInfo
	//if err := json.NewDecoder(r.Body).Decode(&announcement); err != nil {
	//	jsonResponse(w, 400, "Invalid request body", nil)
	//	return
	//}
	//
	//// 添加公告逻辑
	//err := model.AddAnnouncement(ctx, announcement.Title, announcement.Content,
	//	announcement.StartTime, announcement.EndTime)
	//if err != nil {
	//	jsonResponse(w, 500, "Failed to add announcement", nil)
	//	return
	//}

	jsonResponse(w, 200, "Success", nil)
}
