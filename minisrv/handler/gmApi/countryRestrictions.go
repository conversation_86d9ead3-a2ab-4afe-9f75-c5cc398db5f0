package gmApi

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/module/rank"
	"encoding/json"
	"github.com/julienschmidt/httprouter"
	"net/http"
	"os"
)

// CountryRestrictionConfig 表示国家限制配置
type CountryRestrictionConfig struct {
	Enabled          bool     `json:"enabled"`
	Channels         []string `json:"channels"`
	Languages        []string `json:"languages"`
	AllowedCountries []string `json:"allowedCountries"`
	WhitelistIPs     []string `json:"whitelistIPs"`
}

const (
	configDir       = "./data/config"
	restrictionFile = "country_restrictions.json"
)

// GetCountryRestrictions 获取当前的国家限制配置
func GetCountryRestrictions(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	// 验证GM Token
	//if !verifyGMToken(r) {
	//	http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//	return
	//}

	config, err := LoadRestrictionConfig()
	if err != nil {
		// 如果配置文件不存在，返回默认配置
		if os.IsNotExist(err) {
			defaultConfig := CountryRestrictionConfig{
				Enabled:          false,
				Channels:         []string{},
				Languages:        []string{},
				AllowedCountries: []string{},
				WhitelistIPs:     []string{},
			}
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(defaultConfig)
			return
		}

		//http.Error(w, "Failed to load configuration: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// SaveCountryRestrictions 保存国家限制配置
func SaveCountryRestrictions(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	// 验证GM Token
	//if !verifyGMToken(r) {
	//	http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//	return
	//}

	var config CountryRestrictionConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		http.Error(w, "Invalid request body: "+err.Error(), http.StatusBadRequest)
		return
	}

	if err := saveRestrictionConfig(config); err != nil {
		http.Error(w, "Failed to save configuration: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]bool{"success": true})
}

// TestIPRestriction 测试IP是否受到限制
func TestIPRestriction(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	// 验证GM Token
	//if !verifyGMToken(r) {
	//    http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//    return
	//}

	var testRequest struct {
		IP       string `json:"ip"`
		Channel  string `json:"channel"`
		Language string `json:"language"`
	}

	if err := json.NewDecoder(r.Body).Decode(&testRequest); err != nil {
		http.Error(w, "Invalid request body: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 获取IP对应的国家代码
	country := getCountryFromIP(testRequest.IP)

	// 加载当前限制配置
	config, err := LoadRestrictionConfig()
	if err != nil {
		http.Error(w, "Failed to load configuration: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 检查IP是否在白名单中
	allowed := isIPInWhitelist(testRequest.IP, config.WhitelistIPs)

	// 如果不在白名单中，检查国家是否被允许
	if !allowed && config.Enabled {
		// 检查渠道和语言是否受限
		channelRestricted := len(config.Channels) == 0 || contains(config.Channels, testRequest.Channel)
		languageRestricted := len(config.Languages) == 0 || contains(config.Languages, testRequest.Language)

		// 如果渠道和语言受限，检查国家是否在允许列表中
		if channelRestricted && languageRestricted {
			allowed = contains(config.AllowedCountries, country)
		} else {
			// 如果渠道或语言不受限，则允许访问
			allowed = true
		}
	} else if !config.Enabled {
		// 如果限制未启用，则允许所有访问
		allowed = true
	}

	response := map[string]interface{}{
		"ip":      testRequest.IP,
		"country": country,
		"allowed": allowed,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 加载限制配置
func LoadRestrictionConfig() (CountryRestrictionConfig, error) {
	var config CountryRestrictionConfig

	// 确保配置目录存在
	key := rank.Country_Restriction_WhiteList_Key // 使用一个唯一的键格式
	rdb := rank.GetRankClient()
	data, err := rdb.Get(key).Bytes()
	if err != nil {
		return config, err
	}

	if err := json.Unmarshal(data, &config); err != nil {
		return config, err
	}

	return config, nil
}

// 保存限制配置
func saveRestrictionConfig(config CountryRestrictionConfig) error {
	// 确保配置目录存在
	key := rank.Country_Restriction_WhiteList_Key // 使用一个唯一的键格式
	rdb := rank.GetRankClient()
	data, err := json.MarshalIndent(config, "", "  ")
	_, err = rdb.Set(key, data, 0).Result()

	if err != nil {
		return err
	}

	return nil
}

// 检查IP是否在白名单中
func isIPInWhitelist(ip string, whitelist []string) bool {
	for _, whiteIP := range whitelist {
		if ip == whiteIP {
			return true
		}
	}
	return false
}

// 获取IP对应的国家代码
// 注意：这里需要集成实际的IP地理位置查询服务
// 这里只是一个简单的示例实现
func getCountryFromIP(ip string) string {
	// TODO: 集成实际的IP地理位置查询服务
	// 这里返回一个示例值
	return "US"
}

// 辅助函数：检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
