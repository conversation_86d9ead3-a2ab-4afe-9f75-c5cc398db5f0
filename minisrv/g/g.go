package g

import (
	"github.com/highras/rtm-server-sdk-go/src/rtm"
	"github.com/oschwald/geoip2-golang"
	"gitlab-ee.funplus.io/watcher/watcher/misc/wetcd"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/gkit"
	"gitlab-ee.funplus.io/watcher/watcher/stargate/gkit/olcache"
	"go.uber.org/atomic"
	"net/http"
)

var (
	Version    = "unknown"
	VersionMsg = "unknown"

	ConfMd5 = "unknown"

	FullVersion = "unknown"

	ServerHash  string
	Name        string
	ProjectCode string
	HttpAddr    string
	ServerReady atomic.Bool

	//RedisConfig wetcd.AtomicConfig

	EtcdClient          *gkit.EtcdClient
	EtcdRegistryLeaseID wetcd.AtomicLeaseID

	StargateMap *gkit.StargateMap

	StartTime int64

	OLCache = olcache.NewOLCache(1024)

	BIConfig   BI
	HttpClient *http.Client
	RootDir    string
	ClientRtm  *rtm.RTMServerClient
	GeoDB      *geoip2.Reader
)

func init() {
	FullVersion = Version + ConfMd5
}
