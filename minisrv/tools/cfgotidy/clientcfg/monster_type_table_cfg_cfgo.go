// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"text/template"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (t MonsterTypeTable) saveJson(dir string) error {
	return t.SaveJsonWithSuffix(dir, "", t.Filter<PERSON>(func(v *MonsterTypeTableCfg) bool {
		return true
	}))
}

func (t MonsterTypeTable) SaveJsonWithSuffix(dir string, suffix string, cfgs []*MonsterTypeTableCfg) error {
	jsonPath := filepath.Join(dir, "MonsterTypeTable"+suffix+".json")
	f, err := os.OpenFile(jsonPath, os.O_RDWR|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[MonsterTypeTable]open json failed, path=%s, err=[%w]", jsonPath, err)
	}
	defer f.Close()
	// sort
	s := cfgoMonsterTypeTableSlice{}
	for _, cfgoCfg := range cfgs {
		if _, ok := t.localIds[cfgoCfg.Id]; !ok {
			continue
		}
		s = append(s, cfgoCfg)
	}
	sort.Sort(s)

	w := bufio.NewWriter(f)
	w.WriteString("{\n")
	w.WriteString("\t\"Cells\": ")
	bytes, err := json.MarshalIndent(s, "\t", "\t")
	if err != nil {
		return err
	}
	w.Write(bytes)
	w.WriteString("\n}\n")

	if err = w.Flush(); err != nil {
		return err
	}
	return nil
}

type cfgoMonsterTypeTableSlice []*MonsterTypeTableCfg

func (x cfgoMonsterTypeTableSlice) Len() int           { return len(x) }
func (x cfgoMonsterTypeTableSlice) Less(i, j int) bool { return x[i].Id < x[j].Id }
func (x cfgoMonsterTypeTableSlice) Swap(i, j int)      { x[i], x[j] = x[j], x[i] }

func (t *MonsterTypeTable) LoadJsonByPath(jsonPath string, configs *Configs) error {
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[MonsterTypeTable]read json failed, path=%s, err=[%w]", jsonPath, err)
	}
	records := make(map[int32]*MonsterTypeTableCfg)
	if err = json.Unmarshal(bytes, &records); err != nil {
		return fmt.Errorf("[MonsterTypeTable]unmarshal json failed, path=%s, err=[%w]", jsonPath, err)
	}
	for k, v := range records {
		if err = t.Put(k, v); err != nil {
			return fmt.Errorf("[MonsterTypeTable]put record failed, path=%s, err=[%w]", jsonPath, err)
		}
	}
	if err = t.setupIndexes(); err != nil {
		return fmt.Errorf("[MonsterTypeTable]setup indexes failed, path=%s, err=[%w]", jsonPath, err)
	}
	return nil
}

func (t MonsterTypeTable) saveMeta(dir string) error {
	return t.SaveMetaWithSuffix(dir, "")
}

func (t MonsterTypeTable) SaveMetaWithSuffix(dir string, suffix string) error {
	fbPlus := false
	td := &TableData{}
	if err := json.Unmarshal(string2Bytes(MonsterTypeTableJsonContent), td); err != nil {
		return fmt.Errorf("[client]unmarshal json failed, err=[%w]", err)
	}
	if len(suffix) > 0 {
		td.FileName = td.FileName + suffix
		td.TableName = td.TableName + suffix
	}
	path := filepath.Join(dir, td.FileName+".fbs")
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	defer f.Close()

	tplStr := FlatBufferTableTpl
	if fbPlus {
		tplStr = FlatBufferPlusTableTpl
	}
	tpl, err := template.New("table_schema").Parse(tplStr)
	if err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}

	if err = tpl.Execute(f, td); err != nil {
		return fmt.Errorf("[client]generate table schema failed, path: %s, table_name: %s, err:[%w]", path, td.TableName, err)
	}
	return nil
}

var MonsterTypeTableJsonContent string = `{
		"FileName": "MonsterTypeTable",
		"ConstTable": false,
		"TitleForCodeGen": "// Code generated by Cfgo. DO NOT EDIT.",
		"TableName": "MonsterTypeTable",
		"Key": {
			"FieldName": "Id",
			"FieldType": "int32"
		},
		"Fields": [
			{
				"FieldName": "Name",
				"FieldType": "string"
			},
			{
				"FieldName": "MonsterGrade",
				"FieldType": "int32"
			},
			{
				"FieldName": "Pos",
				"FieldType": "int32"
			},
			{
				"FieldName": "Career",
				"FieldType": "int32"
			},
			{
				"FieldName": "MonsterSkill",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "ClaimingDistance",
				"FieldType": "float"
			},
			{
				"FieldName": "Habit",
				"FieldType": "[float]"
			},
			{
				"FieldName": "MoveSpeedTD",
				"FieldType": "float"
			},
			{
				"FieldName": "MoveSpeedPK",
				"FieldType": "float"
			},
			{
				"FieldName": "Immunity",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "BallisticInterception",
				"FieldType": "bool"
			},
			{
				"FieldName": "BallisticDeviation",
				"FieldType": "bool"
			},
			{
				"FieldName": "BallisticEvasionChance",
				"FieldType": "float"
			},
			{
				"FieldName": "CollisionRadius",
				"FieldType": "float"
			},
			{
				"FieldName": "RepelRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "InManual",
				"FieldType": "bool"
			},
			{
				"FieldName": "InManualOrder",
				"FieldType": "int32"
			},
			{
				"FieldName": "UnlockReward",
				"FieldType": "RewardKVS"
			},
			{
				"FieldName": "MonsterDetail",
				"FieldType": "MonsterDetailStr"
			},
			{
				"FieldName": "HeroRec",
				"FieldType": "[int32]"
			},
			{
				"FieldName": "PrefabPass",
				"FieldType": "string"
			},
			{
				"FieldName": "MonsterSpine",
				"FieldType": "string"
			},
			{
				"FieldName": "MonsterImage",
				"FieldType": "string"
			},
			{
				"FieldName": "MonsterImageBottom",
				"FieldType": "string"
			},
			{
				"FieldName": "ModelHeight",
				"FieldType": "float"
			},
			{
				"FieldName": "ManualScaleRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "PreviewScaleRatio",
				"FieldType": "float"
			},
			{
				"FieldName": "MonsterSpineUse",
				"FieldType": "[MonsterSpineStr]"
			},
			{
				"FieldName": "MonsterDeathAudio",
				"FieldType": "string"
			},
			{
				"FieldName": "MoveSlowlyAction",
				"FieldType": "string"
			},
			{
				"FieldName": "MoveSlowlySpeedRange",
				"FieldType": "[float]"
			},
			{
				"FieldName": "MoveSlowlySpeed",
				"FieldType": "float"
			},
			{
				"FieldName": "MoveFastAction",
				"FieldType": "string"
			},
			{
				"FieldName": "MoveFastSpeedRange",
				"FieldType": "[float]"
			},
			{
				"FieldName": "MoveFastSpeed",
				"FieldType": "float"
			}
		]
	}`
