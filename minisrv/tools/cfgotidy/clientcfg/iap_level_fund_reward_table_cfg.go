// Code generated by Cfgo. DO NOT EDIT.
package clientcfg

import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
)

type IapLevelFundRewardTableCfg struct {
	Id         int32                 `json:"Id"`         // Id
	StringId   string                `json:"StringId"`   // StringId
	Fund       int32                 `json:"Fund"`       // 所属基金
	FundRef    *IapLevelFundTableCfg `json:"-"`          // 所属基金
	Level      int32                 `json:"Level"`      // 通过关卡
	LevelRef   *MainLevelTableCfg    `json:"-"`          // 通过关卡
	RewardFree *RewardKVS            `json:"RewardFree"` // 免费奖励
	RewardVip  []*RewardKVS          `json:"RewardVip"`  // 付费奖励
}

func NewIapLevelFundRewardTableCfg() *IapLevelFundRewardTableCfg {
	return &IapLevelFundRewardTableCfg{
		Id:         0,
		StringId:   "",
		Fund:       0,
		FundRef:    nil,
		Level:      0,
		LevelRef:   nil,
		RewardFree: NewRewardKVS(),
		RewardVip:  []*RewardKVS{},
	}
}

func NewMockIapLevelFundRewardTableCfg() *IapLevelFundRewardTableCfg {
	return &IapLevelFundRewardTableCfg{
		Id:         0,
		StringId:   "",
		Fund:       0,
		FundRef:    nil,
		Level:      0,
		LevelRef:   nil,
		RewardFree: NewMockRewardKVS(),
		RewardVip:  []*RewardKVS{NewMockRewardKVS()},
	}
}

type IapLevelFundRewardTable struct {
	configs          *Configs
	localCsvRecords  [][]string
	stringId2IdInCsv map[string]string
	namesInCsv       []string
	name2Index       map[string]int
	records          map[int32]*IapLevelFundRewardTableCfg
	localIds         map[int32]struct{}
}

func NewIapLevelFundRewardTable(configs *Configs) *IapLevelFundRewardTable {
	return &IapLevelFundRewardTable{
		configs:          configs,
		stringId2IdInCsv: map[string]string{},
		name2Index:       map[string]int{},
		records:          map[int32]*IapLevelFundRewardTableCfg{},
		localIds:         map[int32]struct{}{},
	}
}

func (t *IapLevelFundRewardTable) Get(key int32) *IapLevelFundRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapLevelFundRewardTable) GetAll() map[int32]*IapLevelFundRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapLevelFundRewardTable) put(key int32, value *IapLevelFundRewardTableCfg, local bool) *IapLevelFundRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

func (t *IapLevelFundRewardTable) putFromInheritedTable(key int32, value *IapLevelFundRewardTableCfg) error {
	if old := t.put(key, value, false); old != nil {
		return fmt.Errorf("[IapLevelFundRewardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapLevelFundRewardTable) Put(key int32, value *IapLevelFundRewardTableCfg) error {
	if old := t.put(key, value, true); old != nil {
		return fmt.Errorf("[IapLevelFundRewardTable]duplicated Id: Id=%d", key)
	}

	return nil
}

func (t *IapLevelFundRewardTable) PutAll(m map[int32]*IapLevelFundRewardTableCfg) []error {
	var errs []error
	for k, v := range m {
		if err := t.Put(k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errs
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapLevelFundRewardTable) Range(f func(v *IapLevelFundRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapLevelFundRewardTable) Filter(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) map[int32]*IapLevelFundRewardTableCfg {
	filtered := map[int32]*IapLevelFundRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapLevelFundRewardTable) FilterSlice(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) []*IapLevelFundRewardTableCfg {
	filtered := []*IapLevelFundRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapLevelFundRewardTable) FilterKeys(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapLevelFundRewardTable) satisfied(v *IapLevelFundRewardTableCfg, filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapLevelFundRewardTable) setupIndexes() error {
	return nil
}

func (t *IapLevelFundRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapLevelFundRewardTableCfg) bindRefs(c *Configs) {
	r.FundRef = c.IapLevelFundTable.Get(r.Fund)
	r.LevelRef = c.MainLevelTable.Get(r.Level)
	r.RewardFree.bindRefs(c)
	for _, e := range r.RewardVip {
		e.bindRefs(c)
	}
}

func (t *IapLevelFundRewardTable) unmarshalCsv(dir string, configs *Configs, debugMode bool) (recoverErr error) {
	defer func() {
		if err := recover(); err != nil {
			recoverErr = fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv failed: %s", err)
		}
	}()
	for _, record := range t.localCsvRecords {
		recordCfg := NewIapLevelFundRewardTableCfg()
		// Id
		{
			if record[t.getIndexInCsv("Id")] == "" {
				recordCfg.Id = 0
			} else {
				cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("Id")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("Id")], err)
					} else {
						return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, varName=Id, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("Id")], err)
					}
				}
				recordCfg.Id = int32(cfgoInt)
			}
		}
		// StringId
		{
			recordCfg.StringId = strings.TrimSpace(record[t.getIndexInCsv("StringId")])
		}
		// Fund
		if record[t.getIndexInCsv("Fund")] == "" {
			recordCfg.Fund = 0
		} else {
			var err error
			recordCfg.Fund, err = configs.IapLevelFundTable.getIdByRef(record[t.getIndexInCsv("Fund")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, varName=Fund, type=ref@IapLevelFundTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Fund")], err)
				} else {
					return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, varName=Fund, type=ref@IapLevelFundTable, value=%s, err:[%s]", record[t.getIndexInCsv("Fund")], err)
				}
			}
		}
		// Level
		if record[t.getIndexInCsv("Level")] == "" {
			recordCfg.Level = 0
		} else {
			var err error
			recordCfg.Level, err = configs.MainLevelTable.getIdByRef(record[t.getIndexInCsv("Level")])
			if err != nil {
				if debugMode {
					fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, varName=Level, type=ref@MainLevelTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("Level")], err)
				} else {
					return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, varName=Level, type=ref@MainLevelTable, value=%s, err:[%s]", record[t.getIndexInCsv("Level")], err)
				}
			}
		}
		// RewardFree
		{
			// RewardType
			if record[t.getIndexInCsv("RewardFreeRewardType")] == "" {
				recordCfg.RewardFree.RewardType = 0
			} else {
				var err error
				recordCfg.RewardFree.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardFreeRewardType")])
				if err != nil {
					if debugMode {
						fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardFreeRewardType")], err)
					} else {
						return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, varName=RewardType, type=ref@ItemTable, value=%s, err:[%s]", record[t.getIndexInCsv("RewardFreeRewardType")], err)
					}
				}
			}
			// RewardValue
			{
				if record[t.getIndexInCsv("RewardFreeRewardValue")] == "" {
					recordCfg.RewardFree.RewardValue = 0
				} else {
					cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardFreeRewardValue")])
					if err != nil {
						if debugMode {
							fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardFreeRewardValue")], err)
						} else {
							return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, varName=RewardValue, type=int, value=%s, err:[%s]", record[t.getIndexInCsv("RewardFreeRewardValue")], err)
						}
					}
					recordCfg.RewardFree.RewardValue = int32(cfgoInt)
				}
			}
		}
		// RewardVip
		{
			cfgoMeetNilForRewardVipOfRecordCfg := false
			// element 0 of RewardVip
			if !cfgoMeetNilForRewardVipOfRecordCfg {
				cfgoMeetNilForRewardVipOfRecordCfg = true
				var cfgoElemOfRewardVipOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("RewardVip1RewardType")] != "" {
						cfgoMeetNilForRewardVipOfRecordCfg = false
						var err error
						cfgoElemOfRewardVipOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardVip1RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardVip1RewardType")], err)
							} else {
								return fmt.Errorf("[IapLevelFundRewardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("RewardVip1RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RewardVip1RewardValue")] != "" {
						cfgoMeetNilForRewardVipOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardVip1RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardVip1RewardValue")], err)
							} else {
								return fmt.Errorf("[IapLevelFundRewardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("RewardVip1RewardValue")], err)
							}
						}
						cfgoElemOfRewardVipOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardVipOfRecordCfg {
					recordCfg.RewardVip = append(recordCfg.RewardVip, cfgoElemOfRewardVipOfRecordCfg)
				}
			}
			// element 1 of RewardVip
			if !cfgoMeetNilForRewardVipOfRecordCfg {
				cfgoMeetNilForRewardVipOfRecordCfg = true
				var cfgoElemOfRewardVipOfRecordCfg *RewardKVS = NewRewardKVS()
				{
					if record[t.getIndexInCsv("RewardVip2RewardType")] != "" {
						cfgoMeetNilForRewardVipOfRecordCfg = false
						var err error
						cfgoElemOfRewardVipOfRecordCfg.RewardType, err = configs.ItemTable.getIdByRef(record[t.getIndexInCsv("RewardVip2RewardType")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardType, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardVip2RewardType")], err)
							} else {
								return fmt.Errorf("[IapLevelFundRewardTable]unmarshal record failed, cannot parse ref@ItemTable in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardType, value=%s, err:[%s]", record[t.getIndexInCsv("RewardVip2RewardType")], err)
							}
						}
					}
				}
				{
					if record[t.getIndexInCsv("RewardVip2RewardValue")] != "" {
						cfgoMeetNilForRewardVipOfRecordCfg = false
						cfgoInt, err := strconv.Atoi(record[t.getIndexInCsv("RewardVip2RewardValue")])
						if err != nil {
							if debugMode {
								fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardValue, value=%s, err:[%s]\n", record[t.getIndexInCsv("RewardVip2RewardValue")], err)
							} else {
								return fmt.Errorf("[IapLevelFundRewardTable]unmarshal record failed, cannot parse int in collection, elemVarName=cfgoElemOfRewardVipOfRecordCfg.RewardValue, value=%s, err:[%s]", record[t.getIndexInCsv("RewardVip2RewardValue")], err)
							}
						}
						cfgoElemOfRewardVipOfRecordCfg.RewardValue = int32(cfgoInt)
					}
				}

				if !cfgoMeetNilForRewardVipOfRecordCfg {
					recordCfg.RewardVip = append(recordCfg.RewardVip, cfgoElemOfRewardVipOfRecordCfg)
				}
			}

		}
		if err := t.Put(recordCfg.Id, recordCfg); err != nil {
			if debugMode {
				fmt.Printf("ERROR [IapLevelFundRewardTable]unmarshal csv record failed, cause=[%s]", err.Error())
			} else {
				return fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv record failed, cause=[%w]", err)
			}
		}
	}
	return
}

func (t *IapLevelFundRewardTable) loadCsv(dir string, configs *Configs, debugMode bool) error {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}
	var paths []string
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		fileName := f.Name()
		if (fileName != "IapLevelFundRewardTable.csv") && (!strings.HasPrefix(fileName, "IapLevelFundRewardTable-") || !strings.HasSuffix(fileName, ".csv")) {
			continue
		}
		paths = append(paths, dir+string(os.PathSeparator)+f.Name())
	}
	if len(paths) < 0 {
		return errors.New("no csv files for IapLevelFundRewardTable")
	}
	for _, csvPath := range paths {
		err := func() (recoverErr error) {
			f, err := os.Open(csvPath)
			if err != nil {
				if !os.IsNotExist(err) {
					return errors.New("open csv failed: " + csvPath)
				}
			}
			defer f.Close()
			r := csv.NewReader(f)
			defer func() {
				if err := recover(); err != nil {
					recoverErr = fmt.Errorf("[IapLevelFundRewardTable]unmarshal csv failed: %s", err)
				}
			}()
			cfgoLineIndex := 0
			// 每个策划分表中的列顺序可能是不一致的，使用indexesMap进行转换
			var indexesMap []int
			for {
				record, err := r.Read()
				if err != nil {
					if err == io.EOF {
						break
					}
					return fmt.Errorf("[IapLevelFundRewardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
				}
				cfgoLineIndex = cfgoLineIndex + 1
				if cfgoLineIndex <= 2 {
					continue
				}
				if cfgoLineIndex == 3 {
					if len(t.name2Index) > 0 {
						for i := 0; i < len(record); i++ {
							if len(record[i]) <= 0 {
								indexesMap = append(indexesMap, -1)
								continue
							}
							index, ok := t.name2Index[strings.ToLower(record[i])]
							if !ok {
								return fmt.Errorf("[IapLevelFundRewardTable]table heads are mismatched in multi csv: path=%s", csvPath)
							}
							indexesMap = append(indexesMap, index)
						}
						continue
					}
					t.namesInCsv = record
					for i, name := range record {
						if len(name) <= 0 {
							continue
						}
						lowerName := strings.ToLower(name)
						if _, ok := t.name2Index[lowerName]; ok {
							err := fmt.Errorf("duplicated name %s", name)
							return fmt.Errorf("[IapLevelFundRewardTable]read csv record failed: path=%s, err=[%w]", csvPath, err)
						}
						t.name2Index[lowerName] = i
					}
					if index := t.getIndexInCsv("Id"); index < 0 {
						return fmt.Errorf("[IapLevelFundRewardTable]id column is missing: path=%s", csvPath)
					}
					if index := t.getIndexInCsv("StringId"); index < 0 {
						return fmt.Errorf("[IapLevelFundRewardTable]string id column is missing: path=%s", csvPath)
					}
					continue
				}
				emptyLine := true
				for _, str := range record {
					if len(str) > 0 {
						emptyLine = false
						break
					}
				}
				if emptyLine {
					continue
				}
				if len(indexesMap) > 0 {
					transformedRecord := make([]string, len(record), len(record))
					for i, j := range indexesMap {
						if j >= 0 {
							transformedRecord[j] = record[i]
						}
					}
					record = transformedRecord
				}
				id := record[t.getIndexInCsv("Id")]
				if len(id) <= 0 {
					return fmt.Errorf("[IapLevelFundRewardTable]id is empty: path=%s, line=%d", csvPath, cfgoLineIndex)
				}
				stringId := record[t.getIndexInCsv("StringId")]
				if len(stringId) > 0 {
					if _, ok := t.stringId2IdInCsv[stringId]; ok {
						if !debugMode {
							return fmt.Errorf("[IapLevelFundRewardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s", csvPath, stringId)
						} else {
							fmt.Printf("ERROR [IapLevelFundRewardTable]read csv record failed, duplicated stringId: path=%s, stringId=%s\n", csvPath, stringId)
						}
					}
					t.stringId2IdInCsv[stringId] = id
				}
				t.localCsvRecords = append(t.localCsvRecords, record)

			}

			return recoverErr
		}()
		if err != nil {
			return err
		}
	}
	return nil
}

func (t *IapLevelFundRewardTable) addCsvRecordFromInheritedTable(stringId string, id string, debugMode bool) error {
	if _, ok := t.stringId2IdInCsv[stringId]; ok {
		if !debugMode {
			return fmt.Errorf("[IapLevelFundRewardTable]add csv record failed, duplicated stringId: stringId=%s", stringId)
		} else {
			fmt.Printf("ERROR [IapLevelFundRewardTable]add csv record failed, duplicated stringId: stringId=%s\n", stringId)
		}
	}
	t.stringId2IdInCsv[stringId] = id
	return nil
}

func (t *IapLevelFundRewardTable) getIndexInCsv(name string) int {
	index, ok := t.name2Index[strings.ToLower(name)]
	if !ok {
		fmt.Printf("[IapLevelFundRewardTable]get index error, name: %s\n", name)
		return -1
	}
	return index
}

func (t *IapLevelFundRewardTable) getIdByRef(ref string) (int32, error) {
	idStr, ok := t.stringId2IdInCsv[ref]
	if ok {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			return 0, fmt.Errorf("[IapLevelFundRewardTable]invalid ref string: %s", ref)
		}
		return int32(id), nil
	}
	id, err := strconv.Atoi(ref)
	if err != nil {
		return 0, fmt.Errorf("[IapLevelFundRewardTable]invalid ref string: %s", ref)
	}
	return int32(id), nil
}
