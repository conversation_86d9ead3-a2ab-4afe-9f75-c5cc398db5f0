package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
)

func init() {
	orm.RegisterModel[*GrowthFund](nil)
}

type GrowthFund struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.GrowthFundRecord
	uid int64
}

func (u *GrowthFund) Locker() locker.RWLocker {
	return nil
}

func (u *GrowthFund) LockPriority() int {
	return int(u.Uid())
}

func (u *GrowthFund) IsGroup() bool {
	return false
}

func (u *GrowthFund) GetTaskType() minirpc.TaskType {
	return minirpc.TaskTypeGrowth
}

func GetGrowthFundModel(ctx context.Context, uid int64, fundId int32) (*GrowthFund, error) {
	fund, err := orm.Get[*GrowthFund](ctx, uid, fundId)
	if err != nil {
		return nil, err
	}
	if fund == nil {
		fund, err = orm.Create[*GrowthFund](ctx, &minirpc.GrowthFund{
			Uid:                uid,
			FundId:             fundId,
			BuyTimes:           0,
			NormalRewardStatus: map[int32]int32{},
			VipRewardStatus:    map[int32]int32{},
		})
	}
	return fund, err

}
