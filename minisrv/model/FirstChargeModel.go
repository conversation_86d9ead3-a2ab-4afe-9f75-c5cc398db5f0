package model

import (
	"bitbucket.org/kingsgroup/gog-knights/minirpc"
	"context"
	"github.com/sasha-s/go-deadlock"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/locker"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"sync"
)

func init() {
	orm.RegisterModel[*FirstCharge](nil)
}

type FirstCharge struct {
	mu     deadlock.RWMutex
	locker sync.RWMutex
	minirpc.FirstChargeRecord
	uid int64
}

func (u *FirstCharge) Locker() locker.RWLocker {
	return nil
}

func (u *FirstCharge) LockPriority() int {
	return int(u.Uid())
}

func (u *FirstCharge) IsGroup() bool {
	return false
}

func (u *FirstCharge) GetTaskType() minirpc.TaskType {
	return minirpc.TaskTypeGrowth
}

func GetFirstChargeModel(ctx context.Context, uid int64, chargeId int32) (*FirstCharge, error) {
	m, err := orm.Get[*FirstCharge](ctx, uid, chargeId)
	if err != nil {
		return nil, err
	}
	if m == nil {
		m, err = orm.Create[*FirstCharge](ctx, &minirpc.FirstCharge{
			Uid:           uid,
			ChargeId:      chargeId,
			CollectStatus: map[int32]bool{},
		})
	}
	return m, err

}
