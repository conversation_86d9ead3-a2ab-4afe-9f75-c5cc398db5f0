-- Code generated by wrpc. DO NOT EDIT.

rpcTable = {
	[1095] = "wrpc.AddBlackList";
	[1005] = "wrpc.AddBuilding";
	[1092] = "wrpc.AddFriends";
	[1071] = "wrpc.ApplyJoinAlliance";
	[1011] = "wrpc.BuildWorkHero";
	[1022] = "wrpc.BuildingWorkVillage";
	[1088] = "wrpc.BuyAllianceShop";
	[1087] = "wrpc.CancerJoinAlliance";
	[1026] = "wrpc.CancerResearch";
	[1062] = "wrpc.ChangeAvatar";
	[1081] = "wrpc.ChangeMemberStep";
	[1061] = "wrpc.ChangeName";
	[1090] = "wrpc.CollectAllianceChestReward";
	[1089] = "wrpc.CollectAllianceTaskReward";
	[1067] = "wrpc.CollectDailyChestReward";
	[1066] = "wrpc.CollectDailyTaskReward";
	[1125] = "wrpc.CollectDay7ChestReward";
	[1124] = "wrpc.CollectDay7Reward";
	[1024] = "wrpc.CollectFirstPassReward";
	[1132] = "wrpc.CollectGrowthFundReward";
	[1016] = "wrpc.CollectHeroLotteryAccReward";
	[1017] = "wrpc.CollectHeroLotteryLevelReward";
	[1039] = "wrpc.CollectIdleReward";
	[1127] = "wrpc.CollectMailReward";
	[1028] = "wrpc.CollectMainReward";
	[1029] = "wrpc.CollectMapChapterReward";
	[1063] = "wrpc.CollectMonsterBookReward";
	[1058] = "wrpc.CollectPhotovoltaicReward";
	[1012] = "wrpc.CollectResource";
	[1123] = "wrpc.CollectSign7Reward";
	[1056] = "wrpc.CollectStageLevelRewards";
	[1049] = "wrpc.CollectStageReward";
	[1070] = "wrpc.CreateAlliance";
	[1034] = "wrpc.Debug";
	[1010] = "wrpc.DebugAddItem";
	[1096] = "wrpc.DelBlackList";
	[1093] = "wrpc.DelFriends";
	[1129] = "wrpc.DelMail";
	[1130] = "wrpc.DeleteAllReadMail";
	[1084] = "wrpc.DisbandAlliance";
	[107] = "wrpc.Echo";
	[1075] = "wrpc.EditAllianceAcronym";
	[1078] = "wrpc.EditAllianceFlag";
	[1074] = "wrpc.EditAllianceName";
	[1085] = "wrpc.EditAllianceNotice";
	[1077] = "wrpc.EditAllianceStepName";
	[1076] = "wrpc.EditRecruitSetting";
	[1107] = "wrpc.EnhanceGem";
	[1108] = "wrpc.EquipGem";
	[1030] = "wrpc.FinishBuilding";
	[1023] = "wrpc.FinishDungeonStage";
	[1038] = "wrpc.FinishMainStage";
	[1033] = "wrpc.FinishMapEvent";
	[1031] = "wrpc.FinishResearch";
	[1032] = "wrpc.FinishTrain";
	[1106] = "wrpc.GemCraft";
	[1122] = "wrpc.GetActivityList";
	[1060] = "wrpc.GetAllStageRankInfo";
	[1082] = "wrpc.GetAllianceAppList";
	[1069] = "wrpc.GetAllianceInfo";
	[1086] = "wrpc.GetAllianceList";
	[1072] = "wrpc.GetAllianceMembersInfos";
	[1097] = "wrpc.GetBlackList";
	[1065] = "wrpc.GetDailyTask";
	[1099] = "wrpc.GetFriendAppList";
	[1098] = "wrpc.GetFriendRecommendationList";
	[1094] = "wrpc.GetFriendsList";
	[1050] = "wrpc.GetGiftList";
	[1043] = "wrpc.GetIdleReward";
	[1068] = "wrpc.GetMailList";
	[1044] = "wrpc.GetManifest";
	[1057] = "wrpc.GetPhotovoltaicReward";
	[1104] = "wrpc.GetPowerRankInfo";
	[1054] = "wrpc.GetStageRankInfo";
	[1055] = "wrpc.GetStageRankInfoByStageId";
	[1091] = "wrpc.GetUserInfoList";
	[1083] = "wrpc.HandleAllianceApp";
	[1100] = "wrpc.HandleFriendApp";
	[1047] = "wrpc.HeartBeat";
	[1003] = "wrpc.HelloWorld";
	[1064] = "wrpc.HeroBeKilled";
	[1007] = "wrpc.HeroLottery";
	[1052] = "wrpc.HeroUpgradeGene";
	[1008] = "wrpc.HeroUpgradeLevel";
	[1115] = "wrpc.HeroUpgradeQuality";
	[1009] = "wrpc.HeroUpgradeStar";
	[1102] = "wrpc.HookSendMessage";
	[103] = "wrpc.KickOut";
	[1041] = "wrpc.KillMonster";
	[1073] = "wrpc.LeaveAlliance";
	[1111] = "wrpc.LockGem";
	[1019] = "wrpc.LockHeroDice";
	[1002] = "wrpc.Login";
	[1114] = "wrpc.LordGemRandom";
	[9] = "wrpc.MulticastError";
	[1035] = "wrpc.OnBackCity";
	[108] = "wrpc.PerformUserConnHealthcheck";
	[1051] = "wrpc.PrepareOrder";
	[6001] = "wrpc.PushMsgs";
	[6006] = "wrpc.PushOrderFinish";
	[6004] = "wrpc.PushPBMsgs";
	[6005] = "wrpc.PushPBTopicMsg";
	[1018] = "wrpc.RandomHeroDice";
	[1131] = "wrpc.ReadAndCollectAllMail";
	[1128] = "wrpc.ReadMail";
	[1119] = "wrpc.RefreshDungeonRougeTab";
	[1046] = "wrpc.RefreshRougeSkill";
	[1001] = "wrpc.RegisterUser";
	[1080] = "wrpc.RemoveMember";
	[501] = "wrpc.RequestDispatcher";
	[1040] = "wrpc.ResetMainStage";
	[1120] = "wrpc.SaveFunctionOpen";
	[1121] = "wrpc.SaveGuideProgress";
	[1126] = "wrpc.SelectDay7Reward";
	[1117] = "wrpc.SelectDungeonRougeTab";
	[1053] = "wrpc.SelectEliteRougeSkill";
	[1048] = "wrpc.SelectRougeSkill";
	[1015] = "wrpc.SetDefaultBattlePos";
	[1013] = "wrpc.SetHeroBattlePos";
	[1014] = "wrpc.SetHeroTroop";
	[1101] = "wrpc.SettingAddFriendCondition";
	[4] = "wrpc.SimpleError";
	[1118] = "wrpc.StartDungeon";
	[1042] = "wrpc.StartMainStage";
	[1025] = "wrpc.StartResearch";
	[1103] = "wrpc.SubmitOrder";
	[1116] = "wrpc.SweepDungeon";
	[1059] = "wrpc.SweepMainStage";
	[1113] = "wrpc.SwitchEquipGem";
	[109] = "wrpc.SyncServerLoad";
	[1020] = "wrpc.TrainTroops";
	[1079] = "wrpc.TransferPresident";
	[1109] = "wrpc.UnEquipGem";
	[1112] = "wrpc.UnlockGem";
	[106] = "wrpc.UpdateUserConnWarehouse";
	[101] = "wrpc.UpdateUserTopics";
	[1006] = "wrpc.UpgradeBuilding";
	[1037] = "wrpc.UpgradeDaveLevel";
	[1110] = "wrpc.UpgradeLordEquipGrade";
	[1105] = "wrpc.UpgradeLordEquipLevel";
	[1036] = "wrpc.UpgradeSkillLevel";
	[1045] = "wrpc.UploadDeviceInfo";
	[1021] = "wrpc.UserItem";
	[104] = "wrpc.UserOffline";
	[100] = "wrpc.UserOnline";
	[1004] = "wrpc.VerifyUser";
	[105] = "wrpc.WantUserOfflineNtf";
}
