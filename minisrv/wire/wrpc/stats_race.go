//go:build race

// Code generated by wrpc. DO NOT EDIT.

package wrpc

import (
	"sync/atomic"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var stats callStats

type statsItem struct {
	N, Errs int64
}

func (this *statsItem) IncN() {
	atomic.AddInt64(&this.N, 1)
}

func (this *statsItem) IncErrs() {
	atomic.AddInt64(&this.Errs, 1)
}

func (this *statsItem) Clone() statsItem {
	x := statsItem{
		N:    atomic.LoadInt64(&this.N),
		Errs: atomic.LoadInt64(&this.Errs),
	}
	return x
}

type callStats struct {
	SimpleError                   statsItem
	MulticastError                statsItem
	UserOnline                    statsItem
	UpdateUserTopics              statsItem
	KickOut                       statsItem
	UserOffline                   statsItem
	WantUserOfflineNtf            statsItem
	UpdateUserConnWarehouse       statsItem
	Echo                          statsItem
	PerformUserConnHealthcheck    statsItem
	SyncServerLoad                statsItem
	RequestDispatcher             statsItem
	RegisterUser                  statsItem
	Login                         statsItem
	HelloWorld                    statsItem
	VerifyUser                    statsItem
	AddBuilding                   statsItem
	UpgradeBuilding               statsItem
	HeroLottery                   statsItem
	HeroUpgradeLevel              statsItem
	HeroUpgradeStar               statsItem
	DebugAddItem                  statsItem
	BuildWorkHero                 statsItem
	CollectResource               statsItem
	SetHeroBattlePos              statsItem
	SetHeroTroop                  statsItem
	SetDefaultBattlePos           statsItem
	CollectHeroLotteryAccReward   statsItem
	CollectHeroLotteryLevelReward statsItem
	RandomHeroDice                statsItem
	LockHeroDice                  statsItem
	TrainTroops                   statsItem
	UserItem                      statsItem
	BuildingWorkVillage           statsItem
	FinishDungeonStage            statsItem
	CollectFirstPassReward        statsItem
	StartResearch                 statsItem
	CancerResearch                statsItem
	CollectMainReward             statsItem
	CollectMapChapterReward       statsItem
	FinishBuilding                statsItem
	FinishResearch                statsItem
	FinishTrain                   statsItem
	FinishMapEvent                statsItem
	Debug                         statsItem
	OnBackCity                    statsItem
	UpgradeSkillLevel             statsItem
	UpgradeDaveLevel              statsItem
	FinishMainStage               statsItem
	CollectIdleReward             statsItem
	ResetMainStage                statsItem
	KillMonster                   statsItem
	StartMainStage                statsItem
	GetIdleReward                 statsItem
	GetManifest                   statsItem
	UploadDeviceInfo              statsItem
	RefreshRougeSkill             statsItem
	HeartBeat                     statsItem
	SelectRougeSkill              statsItem
	CollectStageReward            statsItem
	GetGiftList                   statsItem
	PrepareOrder                  statsItem
	HeroUpgradeGene               statsItem
	SelectEliteRougeSkill         statsItem
	GetStageRankInfo              statsItem
	GetStageRankInfoByStageId     statsItem
	CollectStageLevelRewards      statsItem
	GetPhotovoltaicReward         statsItem
	CollectPhotovoltaicReward     statsItem
	SweepMainStage                statsItem
	GetAllStageRankInfo           statsItem
	ChangeName                    statsItem
	ChangeAvatar                  statsItem
	CollectMonsterBookReward      statsItem
	HeroBeKilled                  statsItem
	GetDailyTask                  statsItem
	CollectDailyTaskReward        statsItem
	CollectDailyChestReward       statsItem
	GetMailList                   statsItem
	GetAllianceInfo               statsItem
	CreateAlliance                statsItem
	ApplyJoinAlliance             statsItem
	GetAllianceMembersInfos       statsItem
	LeaveAlliance                 statsItem
	EditAllianceName              statsItem
	EditAllianceAcronym           statsItem
	EditRecruitSetting            statsItem
	EditAllianceStepName          statsItem
	EditAllianceFlag              statsItem
	TransferPresident             statsItem
	RemoveMember                  statsItem
	ChangeMemberStep              statsItem
	GetAllianceAppList            statsItem
	HandleAllianceApp             statsItem
	DisbandAlliance               statsItem
	EditAllianceNotice            statsItem
	GetAllianceList               statsItem
	CancerJoinAlliance            statsItem
	BuyAllianceShop               statsItem
	CollectAllianceTaskReward     statsItem
	CollectAllianceChestReward    statsItem
	GetUserInfoList               statsItem
	AddFriends                    statsItem
	DelFriends                    statsItem
	GetFriendsList                statsItem
	AddBlackList                  statsItem
	DelBlackList                  statsItem
	GetBlackList                  statsItem
	GetFriendRecommendationList   statsItem
	GetFriendAppList              statsItem
	HandleFriendApp               statsItem
	SettingAddFriendCondition     statsItem
	HookSendMessage               statsItem
	SubmitOrder                   statsItem
	GetPowerRankInfo              statsItem
	UpgradeLordEquipLevel         statsItem
	GemCraft                      statsItem
	EnhanceGem                    statsItem
	EquipGem                      statsItem
	UnEquipGem                    statsItem
	UpgradeLordEquipGrade         statsItem
	LockGem                       statsItem
	UnlockGem                     statsItem
	SwitchEquipGem                statsItem
	LordGemRandom                 statsItem
	HeroUpgradeQuality            statsItem
	SweepDungeon                  statsItem
	SelectDungeonRougeTab         statsItem
	StartDungeon                  statsItem
	RefreshDungeonRougeTab        statsItem
	SaveFunctionOpen              statsItem
	SaveGuideProgress             statsItem
	GetActivityList               statsItem
	CollectSign7Reward            statsItem
	CollectDay7Reward             statsItem
	CollectDay7ChestReward        statsItem
	SelectDay7Reward              statsItem
	CollectMailReward             statsItem
	ReadMail                      statsItem
	DelMail                       statsItem
	DeleteAllReadMail             statsItem
	ReadAndCollectAllMail         statsItem
	CollectGrowthFundReward       statsItem
	PushMsgs                      statsItem
	PushPBMsgs                    statsItem
	PushPBTopicMsg                statsItem
	PushOrderFinish               statsItem
}

type callStatsWrapper struct {
	SimpleError                   *statsItem
	MulticastError                *statsItem
	UserOnline                    *statsItem
	UpdateUserTopics              *statsItem
	KickOut                       *statsItem
	UserOffline                   *statsItem
	WantUserOfflineNtf            *statsItem
	UpdateUserConnWarehouse       *statsItem
	Echo                          *statsItem
	PerformUserConnHealthcheck    *statsItem
	SyncServerLoad                *statsItem
	RequestDispatcher             *statsItem
	RegisterUser                  *statsItem
	Login                         *statsItem
	HelloWorld                    *statsItem
	VerifyUser                    *statsItem
	AddBuilding                   *statsItem
	UpgradeBuilding               *statsItem
	HeroLottery                   *statsItem
	HeroUpgradeLevel              *statsItem
	HeroUpgradeStar               *statsItem
	DebugAddItem                  *statsItem
	BuildWorkHero                 *statsItem
	CollectResource               *statsItem
	SetHeroBattlePos              *statsItem
	SetHeroTroop                  *statsItem
	SetDefaultBattlePos           *statsItem
	CollectHeroLotteryAccReward   *statsItem
	CollectHeroLotteryLevelReward *statsItem
	RandomHeroDice                *statsItem
	LockHeroDice                  *statsItem
	TrainTroops                   *statsItem
	UserItem                      *statsItem
	BuildingWorkVillage           *statsItem
	FinishDungeonStage            *statsItem
	CollectFirstPassReward        *statsItem
	StartResearch                 *statsItem
	CancerResearch                *statsItem
	CollectMainReward             *statsItem
	CollectMapChapterReward       *statsItem
	FinishBuilding                *statsItem
	FinishResearch                *statsItem
	FinishTrain                   *statsItem
	FinishMapEvent                *statsItem
	Debug                         *statsItem
	OnBackCity                    *statsItem
	UpgradeSkillLevel             *statsItem
	UpgradeDaveLevel              *statsItem
	FinishMainStage               *statsItem
	CollectIdleReward             *statsItem
	ResetMainStage                *statsItem
	KillMonster                   *statsItem
	StartMainStage                *statsItem
	GetIdleReward                 *statsItem
	GetManifest                   *statsItem
	UploadDeviceInfo              *statsItem
	RefreshRougeSkill             *statsItem
	HeartBeat                     *statsItem
	SelectRougeSkill              *statsItem
	CollectStageReward            *statsItem
	GetGiftList                   *statsItem
	PrepareOrder                  *statsItem
	HeroUpgradeGene               *statsItem
	SelectEliteRougeSkill         *statsItem
	GetStageRankInfo              *statsItem
	GetStageRankInfoByStageId     *statsItem
	CollectStageLevelRewards      *statsItem
	GetPhotovoltaicReward         *statsItem
	CollectPhotovoltaicReward     *statsItem
	SweepMainStage                *statsItem
	GetAllStageRankInfo           *statsItem
	ChangeName                    *statsItem
	ChangeAvatar                  *statsItem
	CollectMonsterBookReward      *statsItem
	HeroBeKilled                  *statsItem
	GetDailyTask                  *statsItem
	CollectDailyTaskReward        *statsItem
	CollectDailyChestReward       *statsItem
	GetMailList                   *statsItem
	GetAllianceInfo               *statsItem
	CreateAlliance                *statsItem
	ApplyJoinAlliance             *statsItem
	GetAllianceMembersInfos       *statsItem
	LeaveAlliance                 *statsItem
	EditAllianceName              *statsItem
	EditAllianceAcronym           *statsItem
	EditRecruitSetting            *statsItem
	EditAllianceStepName          *statsItem
	EditAllianceFlag              *statsItem
	TransferPresident             *statsItem
	RemoveMember                  *statsItem
	ChangeMemberStep              *statsItem
	GetAllianceAppList            *statsItem
	HandleAllianceApp             *statsItem
	DisbandAlliance               *statsItem
	EditAllianceNotice            *statsItem
	GetAllianceList               *statsItem
	CancerJoinAlliance            *statsItem
	BuyAllianceShop               *statsItem
	CollectAllianceTaskReward     *statsItem
	CollectAllianceChestReward    *statsItem
	GetUserInfoList               *statsItem
	AddFriends                    *statsItem
	DelFriends                    *statsItem
	GetFriendsList                *statsItem
	AddBlackList                  *statsItem
	DelBlackList                  *statsItem
	GetBlackList                  *statsItem
	GetFriendRecommendationList   *statsItem
	GetFriendAppList              *statsItem
	HandleFriendApp               *statsItem
	SettingAddFriendCondition     *statsItem
	HookSendMessage               *statsItem
	SubmitOrder                   *statsItem
	GetPowerRankInfo              *statsItem
	UpgradeLordEquipLevel         *statsItem
	GemCraft                      *statsItem
	EnhanceGem                    *statsItem
	EquipGem                      *statsItem
	UnEquipGem                    *statsItem
	UpgradeLordEquipGrade         *statsItem
	LockGem                       *statsItem
	UnlockGem                     *statsItem
	SwitchEquipGem                *statsItem
	LordGemRandom                 *statsItem
	HeroUpgradeQuality            *statsItem
	SweepDungeon                  *statsItem
	SelectDungeonRougeTab         *statsItem
	StartDungeon                  *statsItem
	RefreshDungeonRougeTab        *statsItem
	SaveFunctionOpen              *statsItem
	SaveGuideProgress             *statsItem
	GetActivityList               *statsItem
	CollectSign7Reward            *statsItem
	CollectDay7Reward             *statsItem
	CollectDay7ChestReward        *statsItem
	SelectDay7Reward              *statsItem
	CollectMailReward             *statsItem
	ReadMail                      *statsItem
	DelMail                       *statsItem
	DeleteAllReadMail             *statsItem
	ReadAndCollectAllMail         *statsItem
	CollectGrowthFundReward       *statsItem
	PushMsgs                      *statsItem
	PushPBMsgs                    *statsItem
	PushPBTopicMsg                *statsItem
	PushOrderFinish               *statsItem
}

func Stats() interface{} {
	clone := callStats{
		SimpleError:                   stats.SimpleError.Clone(),
		MulticastError:                stats.MulticastError.Clone(),
		UserOnline:                    stats.UserOnline.Clone(),
		UpdateUserTopics:              stats.UpdateUserTopics.Clone(),
		KickOut:                       stats.KickOut.Clone(),
		UserOffline:                   stats.UserOffline.Clone(),
		WantUserOfflineNtf:            stats.WantUserOfflineNtf.Clone(),
		UpdateUserConnWarehouse:       stats.UpdateUserConnWarehouse.Clone(),
		Echo:                          stats.Echo.Clone(),
		PerformUserConnHealthcheck:    stats.PerformUserConnHealthcheck.Clone(),
		SyncServerLoad:                stats.SyncServerLoad.Clone(),
		RequestDispatcher:             stats.RequestDispatcher.Clone(),
		RegisterUser:                  stats.RegisterUser.Clone(),
		Login:                         stats.Login.Clone(),
		HelloWorld:                    stats.HelloWorld.Clone(),
		VerifyUser:                    stats.VerifyUser.Clone(),
		AddBuilding:                   stats.AddBuilding.Clone(),
		UpgradeBuilding:               stats.UpgradeBuilding.Clone(),
		HeroLottery:                   stats.HeroLottery.Clone(),
		HeroUpgradeLevel:              stats.HeroUpgradeLevel.Clone(),
		HeroUpgradeStar:               stats.HeroUpgradeStar.Clone(),
		DebugAddItem:                  stats.DebugAddItem.Clone(),
		BuildWorkHero:                 stats.BuildWorkHero.Clone(),
		CollectResource:               stats.CollectResource.Clone(),
		SetHeroBattlePos:              stats.SetHeroBattlePos.Clone(),
		SetHeroTroop:                  stats.SetHeroTroop.Clone(),
		SetDefaultBattlePos:           stats.SetDefaultBattlePos.Clone(),
		CollectHeroLotteryAccReward:   stats.CollectHeroLotteryAccReward.Clone(),
		CollectHeroLotteryLevelReward: stats.CollectHeroLotteryLevelReward.Clone(),
		RandomHeroDice:                stats.RandomHeroDice.Clone(),
		LockHeroDice:                  stats.LockHeroDice.Clone(),
		TrainTroops:                   stats.TrainTroops.Clone(),
		UserItem:                      stats.UserItem.Clone(),
		BuildingWorkVillage:           stats.BuildingWorkVillage.Clone(),
		FinishDungeonStage:            stats.FinishDungeonStage.Clone(),
		CollectFirstPassReward:        stats.CollectFirstPassReward.Clone(),
		StartResearch:                 stats.StartResearch.Clone(),
		CancerResearch:                stats.CancerResearch.Clone(),
		CollectMainReward:             stats.CollectMainReward.Clone(),
		CollectMapChapterReward:       stats.CollectMapChapterReward.Clone(),
		FinishBuilding:                stats.FinishBuilding.Clone(),
		FinishResearch:                stats.FinishResearch.Clone(),
		FinishTrain:                   stats.FinishTrain.Clone(),
		FinishMapEvent:                stats.FinishMapEvent.Clone(),
		Debug:                         stats.Debug.Clone(),
		OnBackCity:                    stats.OnBackCity.Clone(),
		UpgradeSkillLevel:             stats.UpgradeSkillLevel.Clone(),
		UpgradeDaveLevel:              stats.UpgradeDaveLevel.Clone(),
		FinishMainStage:               stats.FinishMainStage.Clone(),
		CollectIdleReward:             stats.CollectIdleReward.Clone(),
		ResetMainStage:                stats.ResetMainStage.Clone(),
		KillMonster:                   stats.KillMonster.Clone(),
		StartMainStage:                stats.StartMainStage.Clone(),
		GetIdleReward:                 stats.GetIdleReward.Clone(),
		GetManifest:                   stats.GetManifest.Clone(),
		UploadDeviceInfo:              stats.UploadDeviceInfo.Clone(),
		RefreshRougeSkill:             stats.RefreshRougeSkill.Clone(),
		HeartBeat:                     stats.HeartBeat.Clone(),
		SelectRougeSkill:              stats.SelectRougeSkill.Clone(),
		CollectStageReward:            stats.CollectStageReward.Clone(),
		GetGiftList:                   stats.GetGiftList.Clone(),
		PrepareOrder:                  stats.PrepareOrder.Clone(),
		HeroUpgradeGene:               stats.HeroUpgradeGene.Clone(),
		SelectEliteRougeSkill:         stats.SelectEliteRougeSkill.Clone(),
		GetStageRankInfo:              stats.GetStageRankInfo.Clone(),
		GetStageRankInfoByStageId:     stats.GetStageRankInfoByStageId.Clone(),
		CollectStageLevelRewards:      stats.CollectStageLevelRewards.Clone(),
		GetPhotovoltaicReward:         stats.GetPhotovoltaicReward.Clone(),
		CollectPhotovoltaicReward:     stats.CollectPhotovoltaicReward.Clone(),
		SweepMainStage:                stats.SweepMainStage.Clone(),
		GetAllStageRankInfo:           stats.GetAllStageRankInfo.Clone(),
		ChangeName:                    stats.ChangeName.Clone(),
		ChangeAvatar:                  stats.ChangeAvatar.Clone(),
		CollectMonsterBookReward:      stats.CollectMonsterBookReward.Clone(),
		HeroBeKilled:                  stats.HeroBeKilled.Clone(),
		GetDailyTask:                  stats.GetDailyTask.Clone(),
		CollectDailyTaskReward:        stats.CollectDailyTaskReward.Clone(),
		CollectDailyChestReward:       stats.CollectDailyChestReward.Clone(),
		GetMailList:                   stats.GetMailList.Clone(),
		GetAllianceInfo:               stats.GetAllianceInfo.Clone(),
		CreateAlliance:                stats.CreateAlliance.Clone(),
		ApplyJoinAlliance:             stats.ApplyJoinAlliance.Clone(),
		GetAllianceMembersInfos:       stats.GetAllianceMembersInfos.Clone(),
		LeaveAlliance:                 stats.LeaveAlliance.Clone(),
		EditAllianceName:              stats.EditAllianceName.Clone(),
		EditAllianceAcronym:           stats.EditAllianceAcronym.Clone(),
		EditRecruitSetting:            stats.EditRecruitSetting.Clone(),
		EditAllianceStepName:          stats.EditAllianceStepName.Clone(),
		EditAllianceFlag:              stats.EditAllianceFlag.Clone(),
		TransferPresident:             stats.TransferPresident.Clone(),
		RemoveMember:                  stats.RemoveMember.Clone(),
		ChangeMemberStep:              stats.ChangeMemberStep.Clone(),
		GetAllianceAppList:            stats.GetAllianceAppList.Clone(),
		HandleAllianceApp:             stats.HandleAllianceApp.Clone(),
		DisbandAlliance:               stats.DisbandAlliance.Clone(),
		EditAllianceNotice:            stats.EditAllianceNotice.Clone(),
		GetAllianceList:               stats.GetAllianceList.Clone(),
		CancerJoinAlliance:            stats.CancerJoinAlliance.Clone(),
		BuyAllianceShop:               stats.BuyAllianceShop.Clone(),
		CollectAllianceTaskReward:     stats.CollectAllianceTaskReward.Clone(),
		CollectAllianceChestReward:    stats.CollectAllianceChestReward.Clone(),
		GetUserInfoList:               stats.GetUserInfoList.Clone(),
		AddFriends:                    stats.AddFriends.Clone(),
		DelFriends:                    stats.DelFriends.Clone(),
		GetFriendsList:                stats.GetFriendsList.Clone(),
		AddBlackList:                  stats.AddBlackList.Clone(),
		DelBlackList:                  stats.DelBlackList.Clone(),
		GetBlackList:                  stats.GetBlackList.Clone(),
		GetFriendRecommendationList:   stats.GetFriendRecommendationList.Clone(),
		GetFriendAppList:              stats.GetFriendAppList.Clone(),
		HandleFriendApp:               stats.HandleFriendApp.Clone(),
		SettingAddFriendCondition:     stats.SettingAddFriendCondition.Clone(),
		HookSendMessage:               stats.HookSendMessage.Clone(),
		SubmitOrder:                   stats.SubmitOrder.Clone(),
		GetPowerRankInfo:              stats.GetPowerRankInfo.Clone(),
		UpgradeLordEquipLevel:         stats.UpgradeLordEquipLevel.Clone(),
		GemCraft:                      stats.GemCraft.Clone(),
		EnhanceGem:                    stats.EnhanceGem.Clone(),
		EquipGem:                      stats.EquipGem.Clone(),
		UnEquipGem:                    stats.UnEquipGem.Clone(),
		UpgradeLordEquipGrade:         stats.UpgradeLordEquipGrade.Clone(),
		LockGem:                       stats.LockGem.Clone(),
		UnlockGem:                     stats.UnlockGem.Clone(),
		SwitchEquipGem:                stats.SwitchEquipGem.Clone(),
		LordGemRandom:                 stats.LordGemRandom.Clone(),
		HeroUpgradeQuality:            stats.HeroUpgradeQuality.Clone(),
		SweepDungeon:                  stats.SweepDungeon.Clone(),
		SelectDungeonRougeTab:         stats.SelectDungeonRougeTab.Clone(),
		StartDungeon:                  stats.StartDungeon.Clone(),
		RefreshDungeonRougeTab:        stats.RefreshDungeonRougeTab.Clone(),
		SaveFunctionOpen:              stats.SaveFunctionOpen.Clone(),
		SaveGuideProgress:             stats.SaveGuideProgress.Clone(),
		GetActivityList:               stats.GetActivityList.Clone(),
		CollectSign7Reward:            stats.CollectSign7Reward.Clone(),
		CollectDay7Reward:             stats.CollectDay7Reward.Clone(),
		CollectDay7ChestReward:        stats.CollectDay7ChestReward.Clone(),
		SelectDay7Reward:              stats.SelectDay7Reward.Clone(),
		CollectMailReward:             stats.CollectMailReward.Clone(),
		ReadMail:                      stats.ReadMail.Clone(),
		DelMail:                       stats.DelMail.Clone(),
		DeleteAllReadMail:             stats.DeleteAllReadMail.Clone(),
		ReadAndCollectAllMail:         stats.ReadAndCollectAllMail.Clone(),
		CollectGrowthFundReward:       stats.CollectGrowthFundReward.Clone(),
		PushMsgs:                      stats.PushMsgs.Clone(),
		PushPBMsgs:                    stats.PushPBMsgs.Clone(),
		PushPBTopicMsg:                stats.PushPBTopicMsg.Clone(),
		PushOrderFinish:               stats.PushOrderFinish.Clone(),
	}

	var wrapper callStatsWrapper
	if clone.SimpleError.N > 0 {
		wrapper.SimpleError = &clone.SimpleError
	}
	if clone.MulticastError.N > 0 {
		wrapper.MulticastError = &clone.MulticastError
	}
	if clone.UserOnline.N > 0 {
		wrapper.UserOnline = &clone.UserOnline
	}
	if clone.UpdateUserTopics.N > 0 {
		wrapper.UpdateUserTopics = &clone.UpdateUserTopics
	}
	if clone.KickOut.N > 0 {
		wrapper.KickOut = &clone.KickOut
	}
	if clone.UserOffline.N > 0 {
		wrapper.UserOffline = &clone.UserOffline
	}
	if clone.WantUserOfflineNtf.N > 0 {
		wrapper.WantUserOfflineNtf = &clone.WantUserOfflineNtf
	}
	if clone.UpdateUserConnWarehouse.N > 0 {
		wrapper.UpdateUserConnWarehouse = &clone.UpdateUserConnWarehouse
	}
	if clone.Echo.N > 0 {
		wrapper.Echo = &clone.Echo
	}
	if clone.PerformUserConnHealthcheck.N > 0 {
		wrapper.PerformUserConnHealthcheck = &clone.PerformUserConnHealthcheck
	}
	if clone.SyncServerLoad.N > 0 {
		wrapper.SyncServerLoad = &clone.SyncServerLoad
	}
	if clone.RequestDispatcher.N > 0 {
		wrapper.RequestDispatcher = &clone.RequestDispatcher
	}
	if clone.RegisterUser.N > 0 {
		wrapper.RegisterUser = &clone.RegisterUser
	}
	if clone.Login.N > 0 {
		wrapper.Login = &clone.Login
	}
	if clone.HelloWorld.N > 0 {
		wrapper.HelloWorld = &clone.HelloWorld
	}
	if clone.VerifyUser.N > 0 {
		wrapper.VerifyUser = &clone.VerifyUser
	}
	if clone.AddBuilding.N > 0 {
		wrapper.AddBuilding = &clone.AddBuilding
	}
	if clone.UpgradeBuilding.N > 0 {
		wrapper.UpgradeBuilding = &clone.UpgradeBuilding
	}
	if clone.HeroLottery.N > 0 {
		wrapper.HeroLottery = &clone.HeroLottery
	}
	if clone.HeroUpgradeLevel.N > 0 {
		wrapper.HeroUpgradeLevel = &clone.HeroUpgradeLevel
	}
	if clone.HeroUpgradeStar.N > 0 {
		wrapper.HeroUpgradeStar = &clone.HeroUpgradeStar
	}
	if clone.DebugAddItem.N > 0 {
		wrapper.DebugAddItem = &clone.DebugAddItem
	}
	if clone.BuildWorkHero.N > 0 {
		wrapper.BuildWorkHero = &clone.BuildWorkHero
	}
	if clone.CollectResource.N > 0 {
		wrapper.CollectResource = &clone.CollectResource
	}
	if clone.SetHeroBattlePos.N > 0 {
		wrapper.SetHeroBattlePos = &clone.SetHeroBattlePos
	}
	if clone.SetHeroTroop.N > 0 {
		wrapper.SetHeroTroop = &clone.SetHeroTroop
	}
	if clone.SetDefaultBattlePos.N > 0 {
		wrapper.SetDefaultBattlePos = &clone.SetDefaultBattlePos
	}
	if clone.CollectHeroLotteryAccReward.N > 0 {
		wrapper.CollectHeroLotteryAccReward = &clone.CollectHeroLotteryAccReward
	}
	if clone.CollectHeroLotteryLevelReward.N > 0 {
		wrapper.CollectHeroLotteryLevelReward = &clone.CollectHeroLotteryLevelReward
	}
	if clone.RandomHeroDice.N > 0 {
		wrapper.RandomHeroDice = &clone.RandomHeroDice
	}
	if clone.LockHeroDice.N > 0 {
		wrapper.LockHeroDice = &clone.LockHeroDice
	}
	if clone.TrainTroops.N > 0 {
		wrapper.TrainTroops = &clone.TrainTroops
	}
	if clone.UserItem.N > 0 {
		wrapper.UserItem = &clone.UserItem
	}
	if clone.BuildingWorkVillage.N > 0 {
		wrapper.BuildingWorkVillage = &clone.BuildingWorkVillage
	}
	if clone.FinishDungeonStage.N > 0 {
		wrapper.FinishDungeonStage = &clone.FinishDungeonStage
	}
	if clone.CollectFirstPassReward.N > 0 {
		wrapper.CollectFirstPassReward = &clone.CollectFirstPassReward
	}
	if clone.StartResearch.N > 0 {
		wrapper.StartResearch = &clone.StartResearch
	}
	if clone.CancerResearch.N > 0 {
		wrapper.CancerResearch = &clone.CancerResearch
	}
	if clone.CollectMainReward.N > 0 {
		wrapper.CollectMainReward = &clone.CollectMainReward
	}
	if clone.CollectMapChapterReward.N > 0 {
		wrapper.CollectMapChapterReward = &clone.CollectMapChapterReward
	}
	if clone.FinishBuilding.N > 0 {
		wrapper.FinishBuilding = &clone.FinishBuilding
	}
	if clone.FinishResearch.N > 0 {
		wrapper.FinishResearch = &clone.FinishResearch
	}
	if clone.FinishTrain.N > 0 {
		wrapper.FinishTrain = &clone.FinishTrain
	}
	if clone.FinishMapEvent.N > 0 {
		wrapper.FinishMapEvent = &clone.FinishMapEvent
	}
	if clone.Debug.N > 0 {
		wrapper.Debug = &clone.Debug
	}
	if clone.OnBackCity.N > 0 {
		wrapper.OnBackCity = &clone.OnBackCity
	}
	if clone.UpgradeSkillLevel.N > 0 {
		wrapper.UpgradeSkillLevel = &clone.UpgradeSkillLevel
	}
	if clone.UpgradeDaveLevel.N > 0 {
		wrapper.UpgradeDaveLevel = &clone.UpgradeDaveLevel
	}
	if clone.FinishMainStage.N > 0 {
		wrapper.FinishMainStage = &clone.FinishMainStage
	}
	if clone.CollectIdleReward.N > 0 {
		wrapper.CollectIdleReward = &clone.CollectIdleReward
	}
	if clone.ResetMainStage.N > 0 {
		wrapper.ResetMainStage = &clone.ResetMainStage
	}
	if clone.KillMonster.N > 0 {
		wrapper.KillMonster = &clone.KillMonster
	}
	if clone.StartMainStage.N > 0 {
		wrapper.StartMainStage = &clone.StartMainStage
	}
	if clone.GetIdleReward.N > 0 {
		wrapper.GetIdleReward = &clone.GetIdleReward
	}
	if clone.GetManifest.N > 0 {
		wrapper.GetManifest = &clone.GetManifest
	}
	if clone.UploadDeviceInfo.N > 0 {
		wrapper.UploadDeviceInfo = &clone.UploadDeviceInfo
	}
	if clone.RefreshRougeSkill.N > 0 {
		wrapper.RefreshRougeSkill = &clone.RefreshRougeSkill
	}
	if clone.HeartBeat.N > 0 {
		wrapper.HeartBeat = &clone.HeartBeat
	}
	if clone.SelectRougeSkill.N > 0 {
		wrapper.SelectRougeSkill = &clone.SelectRougeSkill
	}
	if clone.CollectStageReward.N > 0 {
		wrapper.CollectStageReward = &clone.CollectStageReward
	}
	if clone.GetGiftList.N > 0 {
		wrapper.GetGiftList = &clone.GetGiftList
	}
	if clone.PrepareOrder.N > 0 {
		wrapper.PrepareOrder = &clone.PrepareOrder
	}
	if clone.HeroUpgradeGene.N > 0 {
		wrapper.HeroUpgradeGene = &clone.HeroUpgradeGene
	}
	if clone.SelectEliteRougeSkill.N > 0 {
		wrapper.SelectEliteRougeSkill = &clone.SelectEliteRougeSkill
	}
	if clone.GetStageRankInfo.N > 0 {
		wrapper.GetStageRankInfo = &clone.GetStageRankInfo
	}
	if clone.GetStageRankInfoByStageId.N > 0 {
		wrapper.GetStageRankInfoByStageId = &clone.GetStageRankInfoByStageId
	}
	if clone.CollectStageLevelRewards.N > 0 {
		wrapper.CollectStageLevelRewards = &clone.CollectStageLevelRewards
	}
	if clone.GetPhotovoltaicReward.N > 0 {
		wrapper.GetPhotovoltaicReward = &clone.GetPhotovoltaicReward
	}
	if clone.CollectPhotovoltaicReward.N > 0 {
		wrapper.CollectPhotovoltaicReward = &clone.CollectPhotovoltaicReward
	}
	if clone.SweepMainStage.N > 0 {
		wrapper.SweepMainStage = &clone.SweepMainStage
	}
	if clone.GetAllStageRankInfo.N > 0 {
		wrapper.GetAllStageRankInfo = &clone.GetAllStageRankInfo
	}
	if clone.ChangeName.N > 0 {
		wrapper.ChangeName = &clone.ChangeName
	}
	if clone.ChangeAvatar.N > 0 {
		wrapper.ChangeAvatar = &clone.ChangeAvatar
	}
	if clone.CollectMonsterBookReward.N > 0 {
		wrapper.CollectMonsterBookReward = &clone.CollectMonsterBookReward
	}
	if clone.HeroBeKilled.N > 0 {
		wrapper.HeroBeKilled = &clone.HeroBeKilled
	}
	if clone.GetDailyTask.N > 0 {
		wrapper.GetDailyTask = &clone.GetDailyTask
	}
	if clone.CollectDailyTaskReward.N > 0 {
		wrapper.CollectDailyTaskReward = &clone.CollectDailyTaskReward
	}
	if clone.CollectDailyChestReward.N > 0 {
		wrapper.CollectDailyChestReward = &clone.CollectDailyChestReward
	}
	if clone.GetMailList.N > 0 {
		wrapper.GetMailList = &clone.GetMailList
	}
	if clone.GetAllianceInfo.N > 0 {
		wrapper.GetAllianceInfo = &clone.GetAllianceInfo
	}
	if clone.CreateAlliance.N > 0 {
		wrapper.CreateAlliance = &clone.CreateAlliance
	}
	if clone.ApplyJoinAlliance.N > 0 {
		wrapper.ApplyJoinAlliance = &clone.ApplyJoinAlliance
	}
	if clone.GetAllianceMembersInfos.N > 0 {
		wrapper.GetAllianceMembersInfos = &clone.GetAllianceMembersInfos
	}
	if clone.LeaveAlliance.N > 0 {
		wrapper.LeaveAlliance = &clone.LeaveAlliance
	}
	if clone.EditAllianceName.N > 0 {
		wrapper.EditAllianceName = &clone.EditAllianceName
	}
	if clone.EditAllianceAcronym.N > 0 {
		wrapper.EditAllianceAcronym = &clone.EditAllianceAcronym
	}
	if clone.EditRecruitSetting.N > 0 {
		wrapper.EditRecruitSetting = &clone.EditRecruitSetting
	}
	if clone.EditAllianceStepName.N > 0 {
		wrapper.EditAllianceStepName = &clone.EditAllianceStepName
	}
	if clone.EditAllianceFlag.N > 0 {
		wrapper.EditAllianceFlag = &clone.EditAllianceFlag
	}
	if clone.TransferPresident.N > 0 {
		wrapper.TransferPresident = &clone.TransferPresident
	}
	if clone.RemoveMember.N > 0 {
		wrapper.RemoveMember = &clone.RemoveMember
	}
	if clone.ChangeMemberStep.N > 0 {
		wrapper.ChangeMemberStep = &clone.ChangeMemberStep
	}
	if clone.GetAllianceAppList.N > 0 {
		wrapper.GetAllianceAppList = &clone.GetAllianceAppList
	}
	if clone.HandleAllianceApp.N > 0 {
		wrapper.HandleAllianceApp = &clone.HandleAllianceApp
	}
	if clone.DisbandAlliance.N > 0 {
		wrapper.DisbandAlliance = &clone.DisbandAlliance
	}
	if clone.EditAllianceNotice.N > 0 {
		wrapper.EditAllianceNotice = &clone.EditAllianceNotice
	}
	if clone.GetAllianceList.N > 0 {
		wrapper.GetAllianceList = &clone.GetAllianceList
	}
	if clone.CancerJoinAlliance.N > 0 {
		wrapper.CancerJoinAlliance = &clone.CancerJoinAlliance
	}
	if clone.BuyAllianceShop.N > 0 {
		wrapper.BuyAllianceShop = &clone.BuyAllianceShop
	}
	if clone.CollectAllianceTaskReward.N > 0 {
		wrapper.CollectAllianceTaskReward = &clone.CollectAllianceTaskReward
	}
	if clone.CollectAllianceChestReward.N > 0 {
		wrapper.CollectAllianceChestReward = &clone.CollectAllianceChestReward
	}
	if clone.GetUserInfoList.N > 0 {
		wrapper.GetUserInfoList = &clone.GetUserInfoList
	}
	if clone.AddFriends.N > 0 {
		wrapper.AddFriends = &clone.AddFriends
	}
	if clone.DelFriends.N > 0 {
		wrapper.DelFriends = &clone.DelFriends
	}
	if clone.GetFriendsList.N > 0 {
		wrapper.GetFriendsList = &clone.GetFriendsList
	}
	if clone.AddBlackList.N > 0 {
		wrapper.AddBlackList = &clone.AddBlackList
	}
	if clone.DelBlackList.N > 0 {
		wrapper.DelBlackList = &clone.DelBlackList
	}
	if clone.GetBlackList.N > 0 {
		wrapper.GetBlackList = &clone.GetBlackList
	}
	if clone.GetFriendRecommendationList.N > 0 {
		wrapper.GetFriendRecommendationList = &clone.GetFriendRecommendationList
	}
	if clone.GetFriendAppList.N > 0 {
		wrapper.GetFriendAppList = &clone.GetFriendAppList
	}
	if clone.HandleFriendApp.N > 0 {
		wrapper.HandleFriendApp = &clone.HandleFriendApp
	}
	if clone.SettingAddFriendCondition.N > 0 {
		wrapper.SettingAddFriendCondition = &clone.SettingAddFriendCondition
	}
	if clone.HookSendMessage.N > 0 {
		wrapper.HookSendMessage = &clone.HookSendMessage
	}
	if clone.SubmitOrder.N > 0 {
		wrapper.SubmitOrder = &clone.SubmitOrder
	}
	if clone.GetPowerRankInfo.N > 0 {
		wrapper.GetPowerRankInfo = &clone.GetPowerRankInfo
	}
	if clone.UpgradeLordEquipLevel.N > 0 {
		wrapper.UpgradeLordEquipLevel = &clone.UpgradeLordEquipLevel
	}
	if clone.GemCraft.N > 0 {
		wrapper.GemCraft = &clone.GemCraft
	}
	if clone.EnhanceGem.N > 0 {
		wrapper.EnhanceGem = &clone.EnhanceGem
	}
	if clone.EquipGem.N > 0 {
		wrapper.EquipGem = &clone.EquipGem
	}
	if clone.UnEquipGem.N > 0 {
		wrapper.UnEquipGem = &clone.UnEquipGem
	}
	if clone.UpgradeLordEquipGrade.N > 0 {
		wrapper.UpgradeLordEquipGrade = &clone.UpgradeLordEquipGrade
	}
	if clone.LockGem.N > 0 {
		wrapper.LockGem = &clone.LockGem
	}
	if clone.UnlockGem.N > 0 {
		wrapper.UnlockGem = &clone.UnlockGem
	}
	if clone.SwitchEquipGem.N > 0 {
		wrapper.SwitchEquipGem = &clone.SwitchEquipGem
	}
	if clone.LordGemRandom.N > 0 {
		wrapper.LordGemRandom = &clone.LordGemRandom
	}
	if clone.HeroUpgradeQuality.N > 0 {
		wrapper.HeroUpgradeQuality = &clone.HeroUpgradeQuality
	}
	if clone.SweepDungeon.N > 0 {
		wrapper.SweepDungeon = &clone.SweepDungeon
	}
	if clone.SelectDungeonRougeTab.N > 0 {
		wrapper.SelectDungeonRougeTab = &clone.SelectDungeonRougeTab
	}
	if clone.StartDungeon.N > 0 {
		wrapper.StartDungeon = &clone.StartDungeon
	}
	if clone.RefreshDungeonRougeTab.N > 0 {
		wrapper.RefreshDungeonRougeTab = &clone.RefreshDungeonRougeTab
	}
	if clone.SaveFunctionOpen.N > 0 {
		wrapper.SaveFunctionOpen = &clone.SaveFunctionOpen
	}
	if clone.SaveGuideProgress.N > 0 {
		wrapper.SaveGuideProgress = &clone.SaveGuideProgress
	}
	if clone.GetActivityList.N > 0 {
		wrapper.GetActivityList = &clone.GetActivityList
	}
	if clone.CollectSign7Reward.N > 0 {
		wrapper.CollectSign7Reward = &clone.CollectSign7Reward
	}
	if clone.CollectDay7Reward.N > 0 {
		wrapper.CollectDay7Reward = &clone.CollectDay7Reward
	}
	if clone.CollectDay7ChestReward.N > 0 {
		wrapper.CollectDay7ChestReward = &clone.CollectDay7ChestReward
	}
	if clone.SelectDay7Reward.N > 0 {
		wrapper.SelectDay7Reward = &clone.SelectDay7Reward
	}
	if clone.CollectMailReward.N > 0 {
		wrapper.CollectMailReward = &clone.CollectMailReward
	}
	if clone.ReadMail.N > 0 {
		wrapper.ReadMail = &clone.ReadMail
	}
	if clone.DelMail.N > 0 {
		wrapper.DelMail = &clone.DelMail
	}
	if clone.DeleteAllReadMail.N > 0 {
		wrapper.DeleteAllReadMail = &clone.DeleteAllReadMail
	}
	if clone.ReadAndCollectAllMail.N > 0 {
		wrapper.ReadAndCollectAllMail = &clone.ReadAndCollectAllMail
	}
	if clone.CollectGrowthFundReward.N > 0 {
		wrapper.CollectGrowthFundReward = &clone.CollectGrowthFundReward
	}
	if clone.PushMsgs.N > 0 {
		wrapper.PushMsgs = &clone.PushMsgs
	}
	if clone.PushPBMsgs.N > 0 {
		wrapper.PushPBMsgs = &clone.PushPBMsgs
	}
	if clone.PushPBTopicMsg.N > 0 {
		wrapper.PushPBTopicMsg = &clone.PushPBTopicMsg
	}
	if clone.PushOrderFinish.N > 0 {
		wrapper.PushOrderFinish = &clone.PushOrderFinish
	}
	return &wrapper
}

func InheritStats(oldWrapper interface{}) error {
	data, err := json.Marshal(oldWrapper)
	if err != nil {
		return err
	}
	var newWrapper callStatsWrapper
	if err := json.Unmarshal(data, &newWrapper); err != nil {
		return err
	}
	if newWrapper.SimpleError != nil {
		atomic.StoreInt64(&stats.SimpleError.N, newWrapper.SimpleError.N)
		atomic.StoreInt64(&stats.SimpleError.Errs, newWrapper.SimpleError.Errs)
	}
	if newWrapper.MulticastError != nil {
		atomic.StoreInt64(&stats.MulticastError.N, newWrapper.MulticastError.N)
		atomic.StoreInt64(&stats.MulticastError.Errs, newWrapper.MulticastError.Errs)
	}
	if newWrapper.UserOnline != nil {
		atomic.StoreInt64(&stats.UserOnline.N, newWrapper.UserOnline.N)
		atomic.StoreInt64(&stats.UserOnline.Errs, newWrapper.UserOnline.Errs)
	}
	if newWrapper.UpdateUserTopics != nil {
		atomic.StoreInt64(&stats.UpdateUserTopics.N, newWrapper.UpdateUserTopics.N)
		atomic.StoreInt64(&stats.UpdateUserTopics.Errs, newWrapper.UpdateUserTopics.Errs)
	}
	if newWrapper.KickOut != nil {
		atomic.StoreInt64(&stats.KickOut.N, newWrapper.KickOut.N)
		atomic.StoreInt64(&stats.KickOut.Errs, newWrapper.KickOut.Errs)
	}
	if newWrapper.UserOffline != nil {
		atomic.StoreInt64(&stats.UserOffline.N, newWrapper.UserOffline.N)
		atomic.StoreInt64(&stats.UserOffline.Errs, newWrapper.UserOffline.Errs)
	}
	if newWrapper.WantUserOfflineNtf != nil {
		atomic.StoreInt64(&stats.WantUserOfflineNtf.N, newWrapper.WantUserOfflineNtf.N)
		atomic.StoreInt64(&stats.WantUserOfflineNtf.Errs, newWrapper.WantUserOfflineNtf.Errs)
	}
	if newWrapper.UpdateUserConnWarehouse != nil {
		atomic.StoreInt64(&stats.UpdateUserConnWarehouse.N, newWrapper.UpdateUserConnWarehouse.N)
		atomic.StoreInt64(&stats.UpdateUserConnWarehouse.Errs, newWrapper.UpdateUserConnWarehouse.Errs)
	}
	if newWrapper.Echo != nil {
		atomic.StoreInt64(&stats.Echo.N, newWrapper.Echo.N)
		atomic.StoreInt64(&stats.Echo.Errs, newWrapper.Echo.Errs)
	}
	if newWrapper.PerformUserConnHealthcheck != nil {
		atomic.StoreInt64(&stats.PerformUserConnHealthcheck.N, newWrapper.PerformUserConnHealthcheck.N)
		atomic.StoreInt64(&stats.PerformUserConnHealthcheck.Errs, newWrapper.PerformUserConnHealthcheck.Errs)
	}
	if newWrapper.SyncServerLoad != nil {
		atomic.StoreInt64(&stats.SyncServerLoad.N, newWrapper.SyncServerLoad.N)
		atomic.StoreInt64(&stats.SyncServerLoad.Errs, newWrapper.SyncServerLoad.Errs)
	}
	if newWrapper.RequestDispatcher != nil {
		atomic.StoreInt64(&stats.RequestDispatcher.N, newWrapper.RequestDispatcher.N)
		atomic.StoreInt64(&stats.RequestDispatcher.Errs, newWrapper.RequestDispatcher.Errs)
	}
	if newWrapper.RegisterUser != nil {
		atomic.StoreInt64(&stats.RegisterUser.N, newWrapper.RegisterUser.N)
		atomic.StoreInt64(&stats.RegisterUser.Errs, newWrapper.RegisterUser.Errs)
	}
	if newWrapper.Login != nil {
		atomic.StoreInt64(&stats.Login.N, newWrapper.Login.N)
		atomic.StoreInt64(&stats.Login.Errs, newWrapper.Login.Errs)
	}
	if newWrapper.HelloWorld != nil {
		atomic.StoreInt64(&stats.HelloWorld.N, newWrapper.HelloWorld.N)
		atomic.StoreInt64(&stats.HelloWorld.Errs, newWrapper.HelloWorld.Errs)
	}
	if newWrapper.VerifyUser != nil {
		atomic.StoreInt64(&stats.VerifyUser.N, newWrapper.VerifyUser.N)
		atomic.StoreInt64(&stats.VerifyUser.Errs, newWrapper.VerifyUser.Errs)
	}
	if newWrapper.AddBuilding != nil {
		atomic.StoreInt64(&stats.AddBuilding.N, newWrapper.AddBuilding.N)
		atomic.StoreInt64(&stats.AddBuilding.Errs, newWrapper.AddBuilding.Errs)
	}
	if newWrapper.UpgradeBuilding != nil {
		atomic.StoreInt64(&stats.UpgradeBuilding.N, newWrapper.UpgradeBuilding.N)
		atomic.StoreInt64(&stats.UpgradeBuilding.Errs, newWrapper.UpgradeBuilding.Errs)
	}
	if newWrapper.HeroLottery != nil {
		atomic.StoreInt64(&stats.HeroLottery.N, newWrapper.HeroLottery.N)
		atomic.StoreInt64(&stats.HeroLottery.Errs, newWrapper.HeroLottery.Errs)
	}
	if newWrapper.HeroUpgradeLevel != nil {
		atomic.StoreInt64(&stats.HeroUpgradeLevel.N, newWrapper.HeroUpgradeLevel.N)
		atomic.StoreInt64(&stats.HeroUpgradeLevel.Errs, newWrapper.HeroUpgradeLevel.Errs)
	}
	if newWrapper.HeroUpgradeStar != nil {
		atomic.StoreInt64(&stats.HeroUpgradeStar.N, newWrapper.HeroUpgradeStar.N)
		atomic.StoreInt64(&stats.HeroUpgradeStar.Errs, newWrapper.HeroUpgradeStar.Errs)
	}
	if newWrapper.DebugAddItem != nil {
		atomic.StoreInt64(&stats.DebugAddItem.N, newWrapper.DebugAddItem.N)
		atomic.StoreInt64(&stats.DebugAddItem.Errs, newWrapper.DebugAddItem.Errs)
	}
	if newWrapper.BuildWorkHero != nil {
		atomic.StoreInt64(&stats.BuildWorkHero.N, newWrapper.BuildWorkHero.N)
		atomic.StoreInt64(&stats.BuildWorkHero.Errs, newWrapper.BuildWorkHero.Errs)
	}
	if newWrapper.CollectResource != nil {
		atomic.StoreInt64(&stats.CollectResource.N, newWrapper.CollectResource.N)
		atomic.StoreInt64(&stats.CollectResource.Errs, newWrapper.CollectResource.Errs)
	}
	if newWrapper.SetHeroBattlePos != nil {
		atomic.StoreInt64(&stats.SetHeroBattlePos.N, newWrapper.SetHeroBattlePos.N)
		atomic.StoreInt64(&stats.SetHeroBattlePos.Errs, newWrapper.SetHeroBattlePos.Errs)
	}
	if newWrapper.SetHeroTroop != nil {
		atomic.StoreInt64(&stats.SetHeroTroop.N, newWrapper.SetHeroTroop.N)
		atomic.StoreInt64(&stats.SetHeroTroop.Errs, newWrapper.SetHeroTroop.Errs)
	}
	if newWrapper.SetDefaultBattlePos != nil {
		atomic.StoreInt64(&stats.SetDefaultBattlePos.N, newWrapper.SetDefaultBattlePos.N)
		atomic.StoreInt64(&stats.SetDefaultBattlePos.Errs, newWrapper.SetDefaultBattlePos.Errs)
	}
	if newWrapper.CollectHeroLotteryAccReward != nil {
		atomic.StoreInt64(&stats.CollectHeroLotteryAccReward.N, newWrapper.CollectHeroLotteryAccReward.N)
		atomic.StoreInt64(&stats.CollectHeroLotteryAccReward.Errs, newWrapper.CollectHeroLotteryAccReward.Errs)
	}
	if newWrapper.CollectHeroLotteryLevelReward != nil {
		atomic.StoreInt64(&stats.CollectHeroLotteryLevelReward.N, newWrapper.CollectHeroLotteryLevelReward.N)
		atomic.StoreInt64(&stats.CollectHeroLotteryLevelReward.Errs, newWrapper.CollectHeroLotteryLevelReward.Errs)
	}
	if newWrapper.RandomHeroDice != nil {
		atomic.StoreInt64(&stats.RandomHeroDice.N, newWrapper.RandomHeroDice.N)
		atomic.StoreInt64(&stats.RandomHeroDice.Errs, newWrapper.RandomHeroDice.Errs)
	}
	if newWrapper.LockHeroDice != nil {
		atomic.StoreInt64(&stats.LockHeroDice.N, newWrapper.LockHeroDice.N)
		atomic.StoreInt64(&stats.LockHeroDice.Errs, newWrapper.LockHeroDice.Errs)
	}
	if newWrapper.TrainTroops != nil {
		atomic.StoreInt64(&stats.TrainTroops.N, newWrapper.TrainTroops.N)
		atomic.StoreInt64(&stats.TrainTroops.Errs, newWrapper.TrainTroops.Errs)
	}
	if newWrapper.UserItem != nil {
		atomic.StoreInt64(&stats.UserItem.N, newWrapper.UserItem.N)
		atomic.StoreInt64(&stats.UserItem.Errs, newWrapper.UserItem.Errs)
	}
	if newWrapper.BuildingWorkVillage != nil {
		atomic.StoreInt64(&stats.BuildingWorkVillage.N, newWrapper.BuildingWorkVillage.N)
		atomic.StoreInt64(&stats.BuildingWorkVillage.Errs, newWrapper.BuildingWorkVillage.Errs)
	}
	if newWrapper.FinishDungeonStage != nil {
		atomic.StoreInt64(&stats.FinishDungeonStage.N, newWrapper.FinishDungeonStage.N)
		atomic.StoreInt64(&stats.FinishDungeonStage.Errs, newWrapper.FinishDungeonStage.Errs)
	}
	if newWrapper.CollectFirstPassReward != nil {
		atomic.StoreInt64(&stats.CollectFirstPassReward.N, newWrapper.CollectFirstPassReward.N)
		atomic.StoreInt64(&stats.CollectFirstPassReward.Errs, newWrapper.CollectFirstPassReward.Errs)
	}
	if newWrapper.StartResearch != nil {
		atomic.StoreInt64(&stats.StartResearch.N, newWrapper.StartResearch.N)
		atomic.StoreInt64(&stats.StartResearch.Errs, newWrapper.StartResearch.Errs)
	}
	if newWrapper.CancerResearch != nil {
		atomic.StoreInt64(&stats.CancerResearch.N, newWrapper.CancerResearch.N)
		atomic.StoreInt64(&stats.CancerResearch.Errs, newWrapper.CancerResearch.Errs)
	}
	if newWrapper.CollectMainReward != nil {
		atomic.StoreInt64(&stats.CollectMainReward.N, newWrapper.CollectMainReward.N)
		atomic.StoreInt64(&stats.CollectMainReward.Errs, newWrapper.CollectMainReward.Errs)
	}
	if newWrapper.CollectMapChapterReward != nil {
		atomic.StoreInt64(&stats.CollectMapChapterReward.N, newWrapper.CollectMapChapterReward.N)
		atomic.StoreInt64(&stats.CollectMapChapterReward.Errs, newWrapper.CollectMapChapterReward.Errs)
	}
	if newWrapper.FinishBuilding != nil {
		atomic.StoreInt64(&stats.FinishBuilding.N, newWrapper.FinishBuilding.N)
		atomic.StoreInt64(&stats.FinishBuilding.Errs, newWrapper.FinishBuilding.Errs)
	}
	if newWrapper.FinishResearch != nil {
		atomic.StoreInt64(&stats.FinishResearch.N, newWrapper.FinishResearch.N)
		atomic.StoreInt64(&stats.FinishResearch.Errs, newWrapper.FinishResearch.Errs)
	}
	if newWrapper.FinishTrain != nil {
		atomic.StoreInt64(&stats.FinishTrain.N, newWrapper.FinishTrain.N)
		atomic.StoreInt64(&stats.FinishTrain.Errs, newWrapper.FinishTrain.Errs)
	}
	if newWrapper.FinishMapEvent != nil {
		atomic.StoreInt64(&stats.FinishMapEvent.N, newWrapper.FinishMapEvent.N)
		atomic.StoreInt64(&stats.FinishMapEvent.Errs, newWrapper.FinishMapEvent.Errs)
	}
	if newWrapper.Debug != nil {
		atomic.StoreInt64(&stats.Debug.N, newWrapper.Debug.N)
		atomic.StoreInt64(&stats.Debug.Errs, newWrapper.Debug.Errs)
	}
	if newWrapper.OnBackCity != nil {
		atomic.StoreInt64(&stats.OnBackCity.N, newWrapper.OnBackCity.N)
		atomic.StoreInt64(&stats.OnBackCity.Errs, newWrapper.OnBackCity.Errs)
	}
	if newWrapper.UpgradeSkillLevel != nil {
		atomic.StoreInt64(&stats.UpgradeSkillLevel.N, newWrapper.UpgradeSkillLevel.N)
		atomic.StoreInt64(&stats.UpgradeSkillLevel.Errs, newWrapper.UpgradeSkillLevel.Errs)
	}
	if newWrapper.UpgradeDaveLevel != nil {
		atomic.StoreInt64(&stats.UpgradeDaveLevel.N, newWrapper.UpgradeDaveLevel.N)
		atomic.StoreInt64(&stats.UpgradeDaveLevel.Errs, newWrapper.UpgradeDaveLevel.Errs)
	}
	if newWrapper.FinishMainStage != nil {
		atomic.StoreInt64(&stats.FinishMainStage.N, newWrapper.FinishMainStage.N)
		atomic.StoreInt64(&stats.FinishMainStage.Errs, newWrapper.FinishMainStage.Errs)
	}
	if newWrapper.CollectIdleReward != nil {
		atomic.StoreInt64(&stats.CollectIdleReward.N, newWrapper.CollectIdleReward.N)
		atomic.StoreInt64(&stats.CollectIdleReward.Errs, newWrapper.CollectIdleReward.Errs)
	}
	if newWrapper.ResetMainStage != nil {
		atomic.StoreInt64(&stats.ResetMainStage.N, newWrapper.ResetMainStage.N)
		atomic.StoreInt64(&stats.ResetMainStage.Errs, newWrapper.ResetMainStage.Errs)
	}
	if newWrapper.KillMonster != nil {
		atomic.StoreInt64(&stats.KillMonster.N, newWrapper.KillMonster.N)
		atomic.StoreInt64(&stats.KillMonster.Errs, newWrapper.KillMonster.Errs)
	}
	if newWrapper.StartMainStage != nil {
		atomic.StoreInt64(&stats.StartMainStage.N, newWrapper.StartMainStage.N)
		atomic.StoreInt64(&stats.StartMainStage.Errs, newWrapper.StartMainStage.Errs)
	}
	if newWrapper.GetIdleReward != nil {
		atomic.StoreInt64(&stats.GetIdleReward.N, newWrapper.GetIdleReward.N)
		atomic.StoreInt64(&stats.GetIdleReward.Errs, newWrapper.GetIdleReward.Errs)
	}
	if newWrapper.GetManifest != nil {
		atomic.StoreInt64(&stats.GetManifest.N, newWrapper.GetManifest.N)
		atomic.StoreInt64(&stats.GetManifest.Errs, newWrapper.GetManifest.Errs)
	}
	if newWrapper.UploadDeviceInfo != nil {
		atomic.StoreInt64(&stats.UploadDeviceInfo.N, newWrapper.UploadDeviceInfo.N)
		atomic.StoreInt64(&stats.UploadDeviceInfo.Errs, newWrapper.UploadDeviceInfo.Errs)
	}
	if newWrapper.RefreshRougeSkill != nil {
		atomic.StoreInt64(&stats.RefreshRougeSkill.N, newWrapper.RefreshRougeSkill.N)
		atomic.StoreInt64(&stats.RefreshRougeSkill.Errs, newWrapper.RefreshRougeSkill.Errs)
	}
	if newWrapper.HeartBeat != nil {
		atomic.StoreInt64(&stats.HeartBeat.N, newWrapper.HeartBeat.N)
		atomic.StoreInt64(&stats.HeartBeat.Errs, newWrapper.HeartBeat.Errs)
	}
	if newWrapper.SelectRougeSkill != nil {
		atomic.StoreInt64(&stats.SelectRougeSkill.N, newWrapper.SelectRougeSkill.N)
		atomic.StoreInt64(&stats.SelectRougeSkill.Errs, newWrapper.SelectRougeSkill.Errs)
	}
	if newWrapper.CollectStageReward != nil {
		atomic.StoreInt64(&stats.CollectStageReward.N, newWrapper.CollectStageReward.N)
		atomic.StoreInt64(&stats.CollectStageReward.Errs, newWrapper.CollectStageReward.Errs)
	}
	if newWrapper.GetGiftList != nil {
		atomic.StoreInt64(&stats.GetGiftList.N, newWrapper.GetGiftList.N)
		atomic.StoreInt64(&stats.GetGiftList.Errs, newWrapper.GetGiftList.Errs)
	}
	if newWrapper.PrepareOrder != nil {
		atomic.StoreInt64(&stats.PrepareOrder.N, newWrapper.PrepareOrder.N)
		atomic.StoreInt64(&stats.PrepareOrder.Errs, newWrapper.PrepareOrder.Errs)
	}
	if newWrapper.HeroUpgradeGene != nil {
		atomic.StoreInt64(&stats.HeroUpgradeGene.N, newWrapper.HeroUpgradeGene.N)
		atomic.StoreInt64(&stats.HeroUpgradeGene.Errs, newWrapper.HeroUpgradeGene.Errs)
	}
	if newWrapper.SelectEliteRougeSkill != nil {
		atomic.StoreInt64(&stats.SelectEliteRougeSkill.N, newWrapper.SelectEliteRougeSkill.N)
		atomic.StoreInt64(&stats.SelectEliteRougeSkill.Errs, newWrapper.SelectEliteRougeSkill.Errs)
	}
	if newWrapper.GetStageRankInfo != nil {
		atomic.StoreInt64(&stats.GetStageRankInfo.N, newWrapper.GetStageRankInfo.N)
		atomic.StoreInt64(&stats.GetStageRankInfo.Errs, newWrapper.GetStageRankInfo.Errs)
	}
	if newWrapper.GetStageRankInfoByStageId != nil {
		atomic.StoreInt64(&stats.GetStageRankInfoByStageId.N, newWrapper.GetStageRankInfoByStageId.N)
		atomic.StoreInt64(&stats.GetStageRankInfoByStageId.Errs, newWrapper.GetStageRankInfoByStageId.Errs)
	}
	if newWrapper.CollectStageLevelRewards != nil {
		atomic.StoreInt64(&stats.CollectStageLevelRewards.N, newWrapper.CollectStageLevelRewards.N)
		atomic.StoreInt64(&stats.CollectStageLevelRewards.Errs, newWrapper.CollectStageLevelRewards.Errs)
	}
	if newWrapper.GetPhotovoltaicReward != nil {
		atomic.StoreInt64(&stats.GetPhotovoltaicReward.N, newWrapper.GetPhotovoltaicReward.N)
		atomic.StoreInt64(&stats.GetPhotovoltaicReward.Errs, newWrapper.GetPhotovoltaicReward.Errs)
	}
	if newWrapper.CollectPhotovoltaicReward != nil {
		atomic.StoreInt64(&stats.CollectPhotovoltaicReward.N, newWrapper.CollectPhotovoltaicReward.N)
		atomic.StoreInt64(&stats.CollectPhotovoltaicReward.Errs, newWrapper.CollectPhotovoltaicReward.Errs)
	}
	if newWrapper.SweepMainStage != nil {
		atomic.StoreInt64(&stats.SweepMainStage.N, newWrapper.SweepMainStage.N)
		atomic.StoreInt64(&stats.SweepMainStage.Errs, newWrapper.SweepMainStage.Errs)
	}
	if newWrapper.GetAllStageRankInfo != nil {
		atomic.StoreInt64(&stats.GetAllStageRankInfo.N, newWrapper.GetAllStageRankInfo.N)
		atomic.StoreInt64(&stats.GetAllStageRankInfo.Errs, newWrapper.GetAllStageRankInfo.Errs)
	}
	if newWrapper.ChangeName != nil {
		atomic.StoreInt64(&stats.ChangeName.N, newWrapper.ChangeName.N)
		atomic.StoreInt64(&stats.ChangeName.Errs, newWrapper.ChangeName.Errs)
	}
	if newWrapper.ChangeAvatar != nil {
		atomic.StoreInt64(&stats.ChangeAvatar.N, newWrapper.ChangeAvatar.N)
		atomic.StoreInt64(&stats.ChangeAvatar.Errs, newWrapper.ChangeAvatar.Errs)
	}
	if newWrapper.CollectMonsterBookReward != nil {
		atomic.StoreInt64(&stats.CollectMonsterBookReward.N, newWrapper.CollectMonsterBookReward.N)
		atomic.StoreInt64(&stats.CollectMonsterBookReward.Errs, newWrapper.CollectMonsterBookReward.Errs)
	}
	if newWrapper.HeroBeKilled != nil {
		atomic.StoreInt64(&stats.HeroBeKilled.N, newWrapper.HeroBeKilled.N)
		atomic.StoreInt64(&stats.HeroBeKilled.Errs, newWrapper.HeroBeKilled.Errs)
	}
	if newWrapper.GetDailyTask != nil {
		atomic.StoreInt64(&stats.GetDailyTask.N, newWrapper.GetDailyTask.N)
		atomic.StoreInt64(&stats.GetDailyTask.Errs, newWrapper.GetDailyTask.Errs)
	}
	if newWrapper.CollectDailyTaskReward != nil {
		atomic.StoreInt64(&stats.CollectDailyTaskReward.N, newWrapper.CollectDailyTaskReward.N)
		atomic.StoreInt64(&stats.CollectDailyTaskReward.Errs, newWrapper.CollectDailyTaskReward.Errs)
	}
	if newWrapper.CollectDailyChestReward != nil {
		atomic.StoreInt64(&stats.CollectDailyChestReward.N, newWrapper.CollectDailyChestReward.N)
		atomic.StoreInt64(&stats.CollectDailyChestReward.Errs, newWrapper.CollectDailyChestReward.Errs)
	}
	if newWrapper.GetMailList != nil {
		atomic.StoreInt64(&stats.GetMailList.N, newWrapper.GetMailList.N)
		atomic.StoreInt64(&stats.GetMailList.Errs, newWrapper.GetMailList.Errs)
	}
	if newWrapper.GetAllianceInfo != nil {
		atomic.StoreInt64(&stats.GetAllianceInfo.N, newWrapper.GetAllianceInfo.N)
		atomic.StoreInt64(&stats.GetAllianceInfo.Errs, newWrapper.GetAllianceInfo.Errs)
	}
	if newWrapper.CreateAlliance != nil {
		atomic.StoreInt64(&stats.CreateAlliance.N, newWrapper.CreateAlliance.N)
		atomic.StoreInt64(&stats.CreateAlliance.Errs, newWrapper.CreateAlliance.Errs)
	}
	if newWrapper.ApplyJoinAlliance != nil {
		atomic.StoreInt64(&stats.ApplyJoinAlliance.N, newWrapper.ApplyJoinAlliance.N)
		atomic.StoreInt64(&stats.ApplyJoinAlliance.Errs, newWrapper.ApplyJoinAlliance.Errs)
	}
	if newWrapper.GetAllianceMembersInfos != nil {
		atomic.StoreInt64(&stats.GetAllianceMembersInfos.N, newWrapper.GetAllianceMembersInfos.N)
		atomic.StoreInt64(&stats.GetAllianceMembersInfos.Errs, newWrapper.GetAllianceMembersInfos.Errs)
	}
	if newWrapper.LeaveAlliance != nil {
		atomic.StoreInt64(&stats.LeaveAlliance.N, newWrapper.LeaveAlliance.N)
		atomic.StoreInt64(&stats.LeaveAlliance.Errs, newWrapper.LeaveAlliance.Errs)
	}
	if newWrapper.EditAllianceName != nil {
		atomic.StoreInt64(&stats.EditAllianceName.N, newWrapper.EditAllianceName.N)
		atomic.StoreInt64(&stats.EditAllianceName.Errs, newWrapper.EditAllianceName.Errs)
	}
	if newWrapper.EditAllianceAcronym != nil {
		atomic.StoreInt64(&stats.EditAllianceAcronym.N, newWrapper.EditAllianceAcronym.N)
		atomic.StoreInt64(&stats.EditAllianceAcronym.Errs, newWrapper.EditAllianceAcronym.Errs)
	}
	if newWrapper.EditRecruitSetting != nil {
		atomic.StoreInt64(&stats.EditRecruitSetting.N, newWrapper.EditRecruitSetting.N)
		atomic.StoreInt64(&stats.EditRecruitSetting.Errs, newWrapper.EditRecruitSetting.Errs)
	}
	if newWrapper.EditAllianceStepName != nil {
		atomic.StoreInt64(&stats.EditAllianceStepName.N, newWrapper.EditAllianceStepName.N)
		atomic.StoreInt64(&stats.EditAllianceStepName.Errs, newWrapper.EditAllianceStepName.Errs)
	}
	if newWrapper.EditAllianceFlag != nil {
		atomic.StoreInt64(&stats.EditAllianceFlag.N, newWrapper.EditAllianceFlag.N)
		atomic.StoreInt64(&stats.EditAllianceFlag.Errs, newWrapper.EditAllianceFlag.Errs)
	}
	if newWrapper.TransferPresident != nil {
		atomic.StoreInt64(&stats.TransferPresident.N, newWrapper.TransferPresident.N)
		atomic.StoreInt64(&stats.TransferPresident.Errs, newWrapper.TransferPresident.Errs)
	}
	if newWrapper.RemoveMember != nil {
		atomic.StoreInt64(&stats.RemoveMember.N, newWrapper.RemoveMember.N)
		atomic.StoreInt64(&stats.RemoveMember.Errs, newWrapper.RemoveMember.Errs)
	}
	if newWrapper.ChangeMemberStep != nil {
		atomic.StoreInt64(&stats.ChangeMemberStep.N, newWrapper.ChangeMemberStep.N)
		atomic.StoreInt64(&stats.ChangeMemberStep.Errs, newWrapper.ChangeMemberStep.Errs)
	}
	if newWrapper.GetAllianceAppList != nil {
		atomic.StoreInt64(&stats.GetAllianceAppList.N, newWrapper.GetAllianceAppList.N)
		atomic.StoreInt64(&stats.GetAllianceAppList.Errs, newWrapper.GetAllianceAppList.Errs)
	}
	if newWrapper.HandleAllianceApp != nil {
		atomic.StoreInt64(&stats.HandleAllianceApp.N, newWrapper.HandleAllianceApp.N)
		atomic.StoreInt64(&stats.HandleAllianceApp.Errs, newWrapper.HandleAllianceApp.Errs)
	}
	if newWrapper.DisbandAlliance != nil {
		atomic.StoreInt64(&stats.DisbandAlliance.N, newWrapper.DisbandAlliance.N)
		atomic.StoreInt64(&stats.DisbandAlliance.Errs, newWrapper.DisbandAlliance.Errs)
	}
	if newWrapper.EditAllianceNotice != nil {
		atomic.StoreInt64(&stats.EditAllianceNotice.N, newWrapper.EditAllianceNotice.N)
		atomic.StoreInt64(&stats.EditAllianceNotice.Errs, newWrapper.EditAllianceNotice.Errs)
	}
	if newWrapper.GetAllianceList != nil {
		atomic.StoreInt64(&stats.GetAllianceList.N, newWrapper.GetAllianceList.N)
		atomic.StoreInt64(&stats.GetAllianceList.Errs, newWrapper.GetAllianceList.Errs)
	}
	if newWrapper.CancerJoinAlliance != nil {
		atomic.StoreInt64(&stats.CancerJoinAlliance.N, newWrapper.CancerJoinAlliance.N)
		atomic.StoreInt64(&stats.CancerJoinAlliance.Errs, newWrapper.CancerJoinAlliance.Errs)
	}
	if newWrapper.BuyAllianceShop != nil {
		atomic.StoreInt64(&stats.BuyAllianceShop.N, newWrapper.BuyAllianceShop.N)
		atomic.StoreInt64(&stats.BuyAllianceShop.Errs, newWrapper.BuyAllianceShop.Errs)
	}
	if newWrapper.CollectAllianceTaskReward != nil {
		atomic.StoreInt64(&stats.CollectAllianceTaskReward.N, newWrapper.CollectAllianceTaskReward.N)
		atomic.StoreInt64(&stats.CollectAllianceTaskReward.Errs, newWrapper.CollectAllianceTaskReward.Errs)
	}
	if newWrapper.CollectAllianceChestReward != nil {
		atomic.StoreInt64(&stats.CollectAllianceChestReward.N, newWrapper.CollectAllianceChestReward.N)
		atomic.StoreInt64(&stats.CollectAllianceChestReward.Errs, newWrapper.CollectAllianceChestReward.Errs)
	}
	if newWrapper.GetUserInfoList != nil {
		atomic.StoreInt64(&stats.GetUserInfoList.N, newWrapper.GetUserInfoList.N)
		atomic.StoreInt64(&stats.GetUserInfoList.Errs, newWrapper.GetUserInfoList.Errs)
	}
	if newWrapper.AddFriends != nil {
		atomic.StoreInt64(&stats.AddFriends.N, newWrapper.AddFriends.N)
		atomic.StoreInt64(&stats.AddFriends.Errs, newWrapper.AddFriends.Errs)
	}
	if newWrapper.DelFriends != nil {
		atomic.StoreInt64(&stats.DelFriends.N, newWrapper.DelFriends.N)
		atomic.StoreInt64(&stats.DelFriends.Errs, newWrapper.DelFriends.Errs)
	}
	if newWrapper.GetFriendsList != nil {
		atomic.StoreInt64(&stats.GetFriendsList.N, newWrapper.GetFriendsList.N)
		atomic.StoreInt64(&stats.GetFriendsList.Errs, newWrapper.GetFriendsList.Errs)
	}
	if newWrapper.AddBlackList != nil {
		atomic.StoreInt64(&stats.AddBlackList.N, newWrapper.AddBlackList.N)
		atomic.StoreInt64(&stats.AddBlackList.Errs, newWrapper.AddBlackList.Errs)
	}
	if newWrapper.DelBlackList != nil {
		atomic.StoreInt64(&stats.DelBlackList.N, newWrapper.DelBlackList.N)
		atomic.StoreInt64(&stats.DelBlackList.Errs, newWrapper.DelBlackList.Errs)
	}
	if newWrapper.GetBlackList != nil {
		atomic.StoreInt64(&stats.GetBlackList.N, newWrapper.GetBlackList.N)
		atomic.StoreInt64(&stats.GetBlackList.Errs, newWrapper.GetBlackList.Errs)
	}
	if newWrapper.GetFriendRecommendationList != nil {
		atomic.StoreInt64(&stats.GetFriendRecommendationList.N, newWrapper.GetFriendRecommendationList.N)
		atomic.StoreInt64(&stats.GetFriendRecommendationList.Errs, newWrapper.GetFriendRecommendationList.Errs)
	}
	if newWrapper.GetFriendAppList != nil {
		atomic.StoreInt64(&stats.GetFriendAppList.N, newWrapper.GetFriendAppList.N)
		atomic.StoreInt64(&stats.GetFriendAppList.Errs, newWrapper.GetFriendAppList.Errs)
	}
	if newWrapper.HandleFriendApp != nil {
		atomic.StoreInt64(&stats.HandleFriendApp.N, newWrapper.HandleFriendApp.N)
		atomic.StoreInt64(&stats.HandleFriendApp.Errs, newWrapper.HandleFriendApp.Errs)
	}
	if newWrapper.SettingAddFriendCondition != nil {
		atomic.StoreInt64(&stats.SettingAddFriendCondition.N, newWrapper.SettingAddFriendCondition.N)
		atomic.StoreInt64(&stats.SettingAddFriendCondition.Errs, newWrapper.SettingAddFriendCondition.Errs)
	}
	if newWrapper.HookSendMessage != nil {
		atomic.StoreInt64(&stats.HookSendMessage.N, newWrapper.HookSendMessage.N)
		atomic.StoreInt64(&stats.HookSendMessage.Errs, newWrapper.HookSendMessage.Errs)
	}
	if newWrapper.SubmitOrder != nil {
		atomic.StoreInt64(&stats.SubmitOrder.N, newWrapper.SubmitOrder.N)
		atomic.StoreInt64(&stats.SubmitOrder.Errs, newWrapper.SubmitOrder.Errs)
	}
	if newWrapper.GetPowerRankInfo != nil {
		atomic.StoreInt64(&stats.GetPowerRankInfo.N, newWrapper.GetPowerRankInfo.N)
		atomic.StoreInt64(&stats.GetPowerRankInfo.Errs, newWrapper.GetPowerRankInfo.Errs)
	}
	if newWrapper.UpgradeLordEquipLevel != nil {
		atomic.StoreInt64(&stats.UpgradeLordEquipLevel.N, newWrapper.UpgradeLordEquipLevel.N)
		atomic.StoreInt64(&stats.UpgradeLordEquipLevel.Errs, newWrapper.UpgradeLordEquipLevel.Errs)
	}
	if newWrapper.GemCraft != nil {
		atomic.StoreInt64(&stats.GemCraft.N, newWrapper.GemCraft.N)
		atomic.StoreInt64(&stats.GemCraft.Errs, newWrapper.GemCraft.Errs)
	}
	if newWrapper.EnhanceGem != nil {
		atomic.StoreInt64(&stats.EnhanceGem.N, newWrapper.EnhanceGem.N)
		atomic.StoreInt64(&stats.EnhanceGem.Errs, newWrapper.EnhanceGem.Errs)
	}
	if newWrapper.EquipGem != nil {
		atomic.StoreInt64(&stats.EquipGem.N, newWrapper.EquipGem.N)
		atomic.StoreInt64(&stats.EquipGem.Errs, newWrapper.EquipGem.Errs)
	}
	if newWrapper.UnEquipGem != nil {
		atomic.StoreInt64(&stats.UnEquipGem.N, newWrapper.UnEquipGem.N)
		atomic.StoreInt64(&stats.UnEquipGem.Errs, newWrapper.UnEquipGem.Errs)
	}
	if newWrapper.UpgradeLordEquipGrade != nil {
		atomic.StoreInt64(&stats.UpgradeLordEquipGrade.N, newWrapper.UpgradeLordEquipGrade.N)
		atomic.StoreInt64(&stats.UpgradeLordEquipGrade.Errs, newWrapper.UpgradeLordEquipGrade.Errs)
	}
	if newWrapper.LockGem != nil {
		atomic.StoreInt64(&stats.LockGem.N, newWrapper.LockGem.N)
		atomic.StoreInt64(&stats.LockGem.Errs, newWrapper.LockGem.Errs)
	}
	if newWrapper.UnlockGem != nil {
		atomic.StoreInt64(&stats.UnlockGem.N, newWrapper.UnlockGem.N)
		atomic.StoreInt64(&stats.UnlockGem.Errs, newWrapper.UnlockGem.Errs)
	}
	if newWrapper.SwitchEquipGem != nil {
		atomic.StoreInt64(&stats.SwitchEquipGem.N, newWrapper.SwitchEquipGem.N)
		atomic.StoreInt64(&stats.SwitchEquipGem.Errs, newWrapper.SwitchEquipGem.Errs)
	}
	if newWrapper.LordGemRandom != nil {
		atomic.StoreInt64(&stats.LordGemRandom.N, newWrapper.LordGemRandom.N)
		atomic.StoreInt64(&stats.LordGemRandom.Errs, newWrapper.LordGemRandom.Errs)
	}
	if newWrapper.HeroUpgradeQuality != nil {
		atomic.StoreInt64(&stats.HeroUpgradeQuality.N, newWrapper.HeroUpgradeQuality.N)
		atomic.StoreInt64(&stats.HeroUpgradeQuality.Errs, newWrapper.HeroUpgradeQuality.Errs)
	}
	if newWrapper.SweepDungeon != nil {
		atomic.StoreInt64(&stats.SweepDungeon.N, newWrapper.SweepDungeon.N)
		atomic.StoreInt64(&stats.SweepDungeon.Errs, newWrapper.SweepDungeon.Errs)
	}
	if newWrapper.SelectDungeonRougeTab != nil {
		atomic.StoreInt64(&stats.SelectDungeonRougeTab.N, newWrapper.SelectDungeonRougeTab.N)
		atomic.StoreInt64(&stats.SelectDungeonRougeTab.Errs, newWrapper.SelectDungeonRougeTab.Errs)
	}
	if newWrapper.StartDungeon != nil {
		atomic.StoreInt64(&stats.StartDungeon.N, newWrapper.StartDungeon.N)
		atomic.StoreInt64(&stats.StartDungeon.Errs, newWrapper.StartDungeon.Errs)
	}
	if newWrapper.RefreshDungeonRougeTab != nil {
		atomic.StoreInt64(&stats.RefreshDungeonRougeTab.N, newWrapper.RefreshDungeonRougeTab.N)
		atomic.StoreInt64(&stats.RefreshDungeonRougeTab.Errs, newWrapper.RefreshDungeonRougeTab.Errs)
	}
	if newWrapper.SaveFunctionOpen != nil {
		atomic.StoreInt64(&stats.SaveFunctionOpen.N, newWrapper.SaveFunctionOpen.N)
		atomic.StoreInt64(&stats.SaveFunctionOpen.Errs, newWrapper.SaveFunctionOpen.Errs)
	}
	if newWrapper.SaveGuideProgress != nil {
		atomic.StoreInt64(&stats.SaveGuideProgress.N, newWrapper.SaveGuideProgress.N)
		atomic.StoreInt64(&stats.SaveGuideProgress.Errs, newWrapper.SaveGuideProgress.Errs)
	}
	if newWrapper.GetActivityList != nil {
		atomic.StoreInt64(&stats.GetActivityList.N, newWrapper.GetActivityList.N)
		atomic.StoreInt64(&stats.GetActivityList.Errs, newWrapper.GetActivityList.Errs)
	}
	if newWrapper.CollectSign7Reward != nil {
		atomic.StoreInt64(&stats.CollectSign7Reward.N, newWrapper.CollectSign7Reward.N)
		atomic.StoreInt64(&stats.CollectSign7Reward.Errs, newWrapper.CollectSign7Reward.Errs)
	}
	if newWrapper.CollectDay7Reward != nil {
		atomic.StoreInt64(&stats.CollectDay7Reward.N, newWrapper.CollectDay7Reward.N)
		atomic.StoreInt64(&stats.CollectDay7Reward.Errs, newWrapper.CollectDay7Reward.Errs)
	}
	if newWrapper.CollectDay7ChestReward != nil {
		atomic.StoreInt64(&stats.CollectDay7ChestReward.N, newWrapper.CollectDay7ChestReward.N)
		atomic.StoreInt64(&stats.CollectDay7ChestReward.Errs, newWrapper.CollectDay7ChestReward.Errs)
	}
	if newWrapper.SelectDay7Reward != nil {
		atomic.StoreInt64(&stats.SelectDay7Reward.N, newWrapper.SelectDay7Reward.N)
		atomic.StoreInt64(&stats.SelectDay7Reward.Errs, newWrapper.SelectDay7Reward.Errs)
	}
	if newWrapper.CollectMailReward != nil {
		atomic.StoreInt64(&stats.CollectMailReward.N, newWrapper.CollectMailReward.N)
		atomic.StoreInt64(&stats.CollectMailReward.Errs, newWrapper.CollectMailReward.Errs)
	}
	if newWrapper.ReadMail != nil {
		atomic.StoreInt64(&stats.ReadMail.N, newWrapper.ReadMail.N)
		atomic.StoreInt64(&stats.ReadMail.Errs, newWrapper.ReadMail.Errs)
	}
	if newWrapper.DelMail != nil {
		atomic.StoreInt64(&stats.DelMail.N, newWrapper.DelMail.N)
		atomic.StoreInt64(&stats.DelMail.Errs, newWrapper.DelMail.Errs)
	}
	if newWrapper.DeleteAllReadMail != nil {
		atomic.StoreInt64(&stats.DeleteAllReadMail.N, newWrapper.DeleteAllReadMail.N)
		atomic.StoreInt64(&stats.DeleteAllReadMail.Errs, newWrapper.DeleteAllReadMail.Errs)
	}
	if newWrapper.ReadAndCollectAllMail != nil {
		atomic.StoreInt64(&stats.ReadAndCollectAllMail.N, newWrapper.ReadAndCollectAllMail.N)
		atomic.StoreInt64(&stats.ReadAndCollectAllMail.Errs, newWrapper.ReadAndCollectAllMail.Errs)
	}
	if newWrapper.CollectGrowthFundReward != nil {
		atomic.StoreInt64(&stats.CollectGrowthFundReward.N, newWrapper.CollectGrowthFundReward.N)
		atomic.StoreInt64(&stats.CollectGrowthFundReward.Errs, newWrapper.CollectGrowthFundReward.Errs)
	}
	if newWrapper.PushMsgs != nil {
		atomic.StoreInt64(&stats.PushMsgs.N, newWrapper.PushMsgs.N)
		atomic.StoreInt64(&stats.PushMsgs.Errs, newWrapper.PushMsgs.Errs)
	}
	if newWrapper.PushPBMsgs != nil {
		atomic.StoreInt64(&stats.PushPBMsgs.N, newWrapper.PushPBMsgs.N)
		atomic.StoreInt64(&stats.PushPBMsgs.Errs, newWrapper.PushPBMsgs.Errs)
	}
	if newWrapper.PushPBTopicMsg != nil {
		atomic.StoreInt64(&stats.PushPBTopicMsg.N, newWrapper.PushPBTopicMsg.N)
		atomic.StoreInt64(&stats.PushPBTopicMsg.Errs, newWrapper.PushPBTopicMsg.Errs)
	}
	if newWrapper.PushOrderFinish != nil {
		atomic.StoreInt64(&stats.PushOrderFinish.N, newWrapper.PushOrderFinish.N)
		atomic.StoreInt64(&stats.PushOrderFinish.Errs, newWrapper.PushOrderFinish.Errs)
	}
	return nil
}
