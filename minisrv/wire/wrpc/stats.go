//go:build !race

// Code generated by wrpc. DO NOT EDIT.

package wrpc

import (
	"sync/atomic"

	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var stats callStats

type statsItem struct {
	N, Errs int64
}

func (this *statsItem) IncN() {
	atomic.AddInt64(&this.N, 1)
}

func (this *statsItem) IncErrs() {
	atomic.AddInt64(&this.Errs, 1)
}

type callStats struct {
	SimpleError                   statsItem
	MulticastError                statsItem
	UserOnline                    statsItem
	UpdateUserTopics              statsItem
	KickOut                       statsItem
	UserOffline                   statsItem
	WantUserOfflineNtf            statsItem
	UpdateUserConnWarehouse       statsItem
	Echo                          statsItem
	PerformUserConnHealthcheck    statsItem
	SyncServerLoad                statsItem
	RequestDispatcher             statsItem
	RegisterUser                  statsItem
	Login                         statsItem
	HelloWorld                    statsItem
	VerifyUser                    statsItem
	AddBuilding                   statsItem
	UpgradeBuilding               statsItem
	HeroLottery                   statsItem
	HeroUpgradeLevel              statsItem
	HeroUpgradeStar               statsItem
	DebugAddItem                  statsItem
	BuildWorkHero                 statsItem
	CollectResource               statsItem
	SetHeroBattlePos              statsItem
	SetHeroTroop                  statsItem
	SetDefaultBattlePos           statsItem
	CollectHeroLotteryAccReward   statsItem
	CollectHeroLotteryLevelReward statsItem
	RandomHeroDice                statsItem
	LockHeroDice                  statsItem
	TrainTroops                   statsItem
	UserItem                      statsItem
	BuildingWorkVillage           statsItem
	FinishDungeonStage            statsItem
	CollectFirstPassReward        statsItem
	StartResearch                 statsItem
	CancerResearch                statsItem
	CollectMainReward             statsItem
	CollectMapChapterReward       statsItem
	FinishBuilding                statsItem
	FinishResearch                statsItem
	FinishTrain                   statsItem
	FinishMapEvent                statsItem
	Debug                         statsItem
	OnBackCity                    statsItem
	UpgradeSkillLevel             statsItem
	UpgradeDaveLevel              statsItem
	FinishMainStage               statsItem
	CollectIdleReward             statsItem
	ResetMainStage                statsItem
	KillMonster                   statsItem
	StartMainStage                statsItem
	GetIdleReward                 statsItem
	GetManifest                   statsItem
	UploadDeviceInfo              statsItem
	RefreshRougeSkill             statsItem
	HeartBeat                     statsItem
	SelectRougeSkill              statsItem
	CollectStageReward            statsItem
	GetGiftList                   statsItem
	PrepareOrder                  statsItem
	HeroUpgradeGene               statsItem
	SelectEliteRougeSkill         statsItem
	GetStageRankInfo              statsItem
	GetStageRankInfoByStageId     statsItem
	CollectStageLevelRewards      statsItem
	GetPhotovoltaicReward         statsItem
	CollectPhotovoltaicReward     statsItem
	SweepMainStage                statsItem
	GetAllStageRankInfo           statsItem
	ChangeName                    statsItem
	ChangeAvatar                  statsItem
	CollectMonsterBookReward      statsItem
	HeroBeKilled                  statsItem
	GetDailyTask                  statsItem
	CollectDailyTaskReward        statsItem
	CollectDailyChestReward       statsItem
	GetMailList                   statsItem
	GetAllianceInfo               statsItem
	CreateAlliance                statsItem
	ApplyJoinAlliance             statsItem
	GetAllianceMembersInfos       statsItem
	LeaveAlliance                 statsItem
	EditAllianceName              statsItem
	EditAllianceAcronym           statsItem
	EditRecruitSetting            statsItem
	EditAllianceStepName          statsItem
	EditAllianceFlag              statsItem
	TransferPresident             statsItem
	RemoveMember                  statsItem
	ChangeMemberStep              statsItem
	GetAllianceAppList            statsItem
	HandleAllianceApp             statsItem
	DisbandAlliance               statsItem
	EditAllianceNotice            statsItem
	GetAllianceList               statsItem
	CancerJoinAlliance            statsItem
	BuyAllianceShop               statsItem
	CollectAllianceTaskReward     statsItem
	CollectAllianceChestReward    statsItem
	GetUserInfoList               statsItem
	AddFriends                    statsItem
	DelFriends                    statsItem
	GetFriendsList                statsItem
	AddBlackList                  statsItem
	DelBlackList                  statsItem
	GetBlackList                  statsItem
	GetFriendRecommendationList   statsItem
	GetFriendAppList              statsItem
	HandleFriendApp               statsItem
	SettingAddFriendCondition     statsItem
	HookSendMessage               statsItem
	SubmitOrder                   statsItem
	GetPowerRankInfo              statsItem
	UpgradeLordEquipLevel         statsItem
	GemCraft                      statsItem
	EnhanceGem                    statsItem
	EquipGem                      statsItem
	UnEquipGem                    statsItem
	UpgradeLordEquipGrade         statsItem
	LockGem                       statsItem
	UnlockGem                     statsItem
	SwitchEquipGem                statsItem
	LordGemRandom                 statsItem
	HeroUpgradeQuality            statsItem
	SweepDungeon                  statsItem
	SelectDungeonRougeTab         statsItem
	StartDungeon                  statsItem
	RefreshDungeonRougeTab        statsItem
	SaveFunctionOpen              statsItem
	SaveGuideProgress             statsItem
	GetActivityList               statsItem
	CollectSign7Reward            statsItem
	CollectDay7Reward             statsItem
	CollectDay7ChestReward        statsItem
	SelectDay7Reward              statsItem
	CollectMailReward             statsItem
	ReadMail                      statsItem
	DelMail                       statsItem
	DeleteAllReadMail             statsItem
	ReadAndCollectAllMail         statsItem
	CollectGrowthFundReward       statsItem
	PushMsgs                      statsItem
	PushPBMsgs                    statsItem
	PushPBTopicMsg                statsItem
	PushOrderFinish               statsItem
}

type callStatsWrapper struct {
	SimpleError                   *statsItem
	MulticastError                *statsItem
	UserOnline                    *statsItem
	UpdateUserTopics              *statsItem
	KickOut                       *statsItem
	UserOffline                   *statsItem
	WantUserOfflineNtf            *statsItem
	UpdateUserConnWarehouse       *statsItem
	Echo                          *statsItem
	PerformUserConnHealthcheck    *statsItem
	SyncServerLoad                *statsItem
	RequestDispatcher             *statsItem
	RegisterUser                  *statsItem
	Login                         *statsItem
	HelloWorld                    *statsItem
	VerifyUser                    *statsItem
	AddBuilding                   *statsItem
	UpgradeBuilding               *statsItem
	HeroLottery                   *statsItem
	HeroUpgradeLevel              *statsItem
	HeroUpgradeStar               *statsItem
	DebugAddItem                  *statsItem
	BuildWorkHero                 *statsItem
	CollectResource               *statsItem
	SetHeroBattlePos              *statsItem
	SetHeroTroop                  *statsItem
	SetDefaultBattlePos           *statsItem
	CollectHeroLotteryAccReward   *statsItem
	CollectHeroLotteryLevelReward *statsItem
	RandomHeroDice                *statsItem
	LockHeroDice                  *statsItem
	TrainTroops                   *statsItem
	UserItem                      *statsItem
	BuildingWorkVillage           *statsItem
	FinishDungeonStage            *statsItem
	CollectFirstPassReward        *statsItem
	StartResearch                 *statsItem
	CancerResearch                *statsItem
	CollectMainReward             *statsItem
	CollectMapChapterReward       *statsItem
	FinishBuilding                *statsItem
	FinishResearch                *statsItem
	FinishTrain                   *statsItem
	FinishMapEvent                *statsItem
	Debug                         *statsItem
	OnBackCity                    *statsItem
	UpgradeSkillLevel             *statsItem
	UpgradeDaveLevel              *statsItem
	FinishMainStage               *statsItem
	CollectIdleReward             *statsItem
	ResetMainStage                *statsItem
	KillMonster                   *statsItem
	StartMainStage                *statsItem
	GetIdleReward                 *statsItem
	GetManifest                   *statsItem
	UploadDeviceInfo              *statsItem
	RefreshRougeSkill             *statsItem
	HeartBeat                     *statsItem
	SelectRougeSkill              *statsItem
	CollectStageReward            *statsItem
	GetGiftList                   *statsItem
	PrepareOrder                  *statsItem
	HeroUpgradeGene               *statsItem
	SelectEliteRougeSkill         *statsItem
	GetStageRankInfo              *statsItem
	GetStageRankInfoByStageId     *statsItem
	CollectStageLevelRewards      *statsItem
	GetPhotovoltaicReward         *statsItem
	CollectPhotovoltaicReward     *statsItem
	SweepMainStage                *statsItem
	GetAllStageRankInfo           *statsItem
	ChangeName                    *statsItem
	ChangeAvatar                  *statsItem
	CollectMonsterBookReward      *statsItem
	HeroBeKilled                  *statsItem
	GetDailyTask                  *statsItem
	CollectDailyTaskReward        *statsItem
	CollectDailyChestReward       *statsItem
	GetMailList                   *statsItem
	GetAllianceInfo               *statsItem
	CreateAlliance                *statsItem
	ApplyJoinAlliance             *statsItem
	GetAllianceMembersInfos       *statsItem
	LeaveAlliance                 *statsItem
	EditAllianceName              *statsItem
	EditAllianceAcronym           *statsItem
	EditRecruitSetting            *statsItem
	EditAllianceStepName          *statsItem
	EditAllianceFlag              *statsItem
	TransferPresident             *statsItem
	RemoveMember                  *statsItem
	ChangeMemberStep              *statsItem
	GetAllianceAppList            *statsItem
	HandleAllianceApp             *statsItem
	DisbandAlliance               *statsItem
	EditAllianceNotice            *statsItem
	GetAllianceList               *statsItem
	CancerJoinAlliance            *statsItem
	BuyAllianceShop               *statsItem
	CollectAllianceTaskReward     *statsItem
	CollectAllianceChestReward    *statsItem
	GetUserInfoList               *statsItem
	AddFriends                    *statsItem
	DelFriends                    *statsItem
	GetFriendsList                *statsItem
	AddBlackList                  *statsItem
	DelBlackList                  *statsItem
	GetBlackList                  *statsItem
	GetFriendRecommendationList   *statsItem
	GetFriendAppList              *statsItem
	HandleFriendApp               *statsItem
	SettingAddFriendCondition     *statsItem
	HookSendMessage               *statsItem
	SubmitOrder                   *statsItem
	GetPowerRankInfo              *statsItem
	UpgradeLordEquipLevel         *statsItem
	GemCraft                      *statsItem
	EnhanceGem                    *statsItem
	EquipGem                      *statsItem
	UnEquipGem                    *statsItem
	UpgradeLordEquipGrade         *statsItem
	LockGem                       *statsItem
	UnlockGem                     *statsItem
	SwitchEquipGem                *statsItem
	LordGemRandom                 *statsItem
	HeroUpgradeQuality            *statsItem
	SweepDungeon                  *statsItem
	SelectDungeonRougeTab         *statsItem
	StartDungeon                  *statsItem
	RefreshDungeonRougeTab        *statsItem
	SaveFunctionOpen              *statsItem
	SaveGuideProgress             *statsItem
	GetActivityList               *statsItem
	CollectSign7Reward            *statsItem
	CollectDay7Reward             *statsItem
	CollectDay7ChestReward        *statsItem
	SelectDay7Reward              *statsItem
	CollectMailReward             *statsItem
	ReadMail                      *statsItem
	DelMail                       *statsItem
	DeleteAllReadMail             *statsItem
	ReadAndCollectAllMail         *statsItem
	CollectGrowthFundReward       *statsItem
	PushMsgs                      *statsItem
	PushPBMsgs                    *statsItem
	PushPBTopicMsg                *statsItem
	PushOrderFinish               *statsItem
}

func Stats() interface{} {
	clone := stats
	var wrapper callStatsWrapper
	if clone.SimpleError.N > 0 {
		wrapper.SimpleError = &clone.SimpleError
	}
	if clone.MulticastError.N > 0 {
		wrapper.MulticastError = &clone.MulticastError
	}
	if clone.UserOnline.N > 0 {
		wrapper.UserOnline = &clone.UserOnline
	}
	if clone.UpdateUserTopics.N > 0 {
		wrapper.UpdateUserTopics = &clone.UpdateUserTopics
	}
	if clone.KickOut.N > 0 {
		wrapper.KickOut = &clone.KickOut
	}
	if clone.UserOffline.N > 0 {
		wrapper.UserOffline = &clone.UserOffline
	}
	if clone.WantUserOfflineNtf.N > 0 {
		wrapper.WantUserOfflineNtf = &clone.WantUserOfflineNtf
	}
	if clone.UpdateUserConnWarehouse.N > 0 {
		wrapper.UpdateUserConnWarehouse = &clone.UpdateUserConnWarehouse
	}
	if clone.Echo.N > 0 {
		wrapper.Echo = &clone.Echo
	}
	if clone.PerformUserConnHealthcheck.N > 0 {
		wrapper.PerformUserConnHealthcheck = &clone.PerformUserConnHealthcheck
	}
	if clone.SyncServerLoad.N > 0 {
		wrapper.SyncServerLoad = &clone.SyncServerLoad
	}
	if clone.RequestDispatcher.N > 0 {
		wrapper.RequestDispatcher = &clone.RequestDispatcher
	}
	if clone.RegisterUser.N > 0 {
		wrapper.RegisterUser = &clone.RegisterUser
	}
	if clone.Login.N > 0 {
		wrapper.Login = &clone.Login
	}
	if clone.HelloWorld.N > 0 {
		wrapper.HelloWorld = &clone.HelloWorld
	}
	if clone.VerifyUser.N > 0 {
		wrapper.VerifyUser = &clone.VerifyUser
	}
	if clone.AddBuilding.N > 0 {
		wrapper.AddBuilding = &clone.AddBuilding
	}
	if clone.UpgradeBuilding.N > 0 {
		wrapper.UpgradeBuilding = &clone.UpgradeBuilding
	}
	if clone.HeroLottery.N > 0 {
		wrapper.HeroLottery = &clone.HeroLottery
	}
	if clone.HeroUpgradeLevel.N > 0 {
		wrapper.HeroUpgradeLevel = &clone.HeroUpgradeLevel
	}
	if clone.HeroUpgradeStar.N > 0 {
		wrapper.HeroUpgradeStar = &clone.HeroUpgradeStar
	}
	if clone.DebugAddItem.N > 0 {
		wrapper.DebugAddItem = &clone.DebugAddItem
	}
	if clone.BuildWorkHero.N > 0 {
		wrapper.BuildWorkHero = &clone.BuildWorkHero
	}
	if clone.CollectResource.N > 0 {
		wrapper.CollectResource = &clone.CollectResource
	}
	if clone.SetHeroBattlePos.N > 0 {
		wrapper.SetHeroBattlePos = &clone.SetHeroBattlePos
	}
	if clone.SetHeroTroop.N > 0 {
		wrapper.SetHeroTroop = &clone.SetHeroTroop
	}
	if clone.SetDefaultBattlePos.N > 0 {
		wrapper.SetDefaultBattlePos = &clone.SetDefaultBattlePos
	}
	if clone.CollectHeroLotteryAccReward.N > 0 {
		wrapper.CollectHeroLotteryAccReward = &clone.CollectHeroLotteryAccReward
	}
	if clone.CollectHeroLotteryLevelReward.N > 0 {
		wrapper.CollectHeroLotteryLevelReward = &clone.CollectHeroLotteryLevelReward
	}
	if clone.RandomHeroDice.N > 0 {
		wrapper.RandomHeroDice = &clone.RandomHeroDice
	}
	if clone.LockHeroDice.N > 0 {
		wrapper.LockHeroDice = &clone.LockHeroDice
	}
	if clone.TrainTroops.N > 0 {
		wrapper.TrainTroops = &clone.TrainTroops
	}
	if clone.UserItem.N > 0 {
		wrapper.UserItem = &clone.UserItem
	}
	if clone.BuildingWorkVillage.N > 0 {
		wrapper.BuildingWorkVillage = &clone.BuildingWorkVillage
	}
	if clone.FinishDungeonStage.N > 0 {
		wrapper.FinishDungeonStage = &clone.FinishDungeonStage
	}
	if clone.CollectFirstPassReward.N > 0 {
		wrapper.CollectFirstPassReward = &clone.CollectFirstPassReward
	}
	if clone.StartResearch.N > 0 {
		wrapper.StartResearch = &clone.StartResearch
	}
	if clone.CancerResearch.N > 0 {
		wrapper.CancerResearch = &clone.CancerResearch
	}
	if clone.CollectMainReward.N > 0 {
		wrapper.CollectMainReward = &clone.CollectMainReward
	}
	if clone.CollectMapChapterReward.N > 0 {
		wrapper.CollectMapChapterReward = &clone.CollectMapChapterReward
	}
	if clone.FinishBuilding.N > 0 {
		wrapper.FinishBuilding = &clone.FinishBuilding
	}
	if clone.FinishResearch.N > 0 {
		wrapper.FinishResearch = &clone.FinishResearch
	}
	if clone.FinishTrain.N > 0 {
		wrapper.FinishTrain = &clone.FinishTrain
	}
	if clone.FinishMapEvent.N > 0 {
		wrapper.FinishMapEvent = &clone.FinishMapEvent
	}
	if clone.Debug.N > 0 {
		wrapper.Debug = &clone.Debug
	}
	if clone.OnBackCity.N > 0 {
		wrapper.OnBackCity = &clone.OnBackCity
	}
	if clone.UpgradeSkillLevel.N > 0 {
		wrapper.UpgradeSkillLevel = &clone.UpgradeSkillLevel
	}
	if clone.UpgradeDaveLevel.N > 0 {
		wrapper.UpgradeDaveLevel = &clone.UpgradeDaveLevel
	}
	if clone.FinishMainStage.N > 0 {
		wrapper.FinishMainStage = &clone.FinishMainStage
	}
	if clone.CollectIdleReward.N > 0 {
		wrapper.CollectIdleReward = &clone.CollectIdleReward
	}
	if clone.ResetMainStage.N > 0 {
		wrapper.ResetMainStage = &clone.ResetMainStage
	}
	if clone.KillMonster.N > 0 {
		wrapper.KillMonster = &clone.KillMonster
	}
	if clone.StartMainStage.N > 0 {
		wrapper.StartMainStage = &clone.StartMainStage
	}
	if clone.GetIdleReward.N > 0 {
		wrapper.GetIdleReward = &clone.GetIdleReward
	}
	if clone.GetManifest.N > 0 {
		wrapper.GetManifest = &clone.GetManifest
	}
	if clone.UploadDeviceInfo.N > 0 {
		wrapper.UploadDeviceInfo = &clone.UploadDeviceInfo
	}
	if clone.RefreshRougeSkill.N > 0 {
		wrapper.RefreshRougeSkill = &clone.RefreshRougeSkill
	}
	if clone.HeartBeat.N > 0 {
		wrapper.HeartBeat = &clone.HeartBeat
	}
	if clone.SelectRougeSkill.N > 0 {
		wrapper.SelectRougeSkill = &clone.SelectRougeSkill
	}
	if clone.CollectStageReward.N > 0 {
		wrapper.CollectStageReward = &clone.CollectStageReward
	}
	if clone.GetGiftList.N > 0 {
		wrapper.GetGiftList = &clone.GetGiftList
	}
	if clone.PrepareOrder.N > 0 {
		wrapper.PrepareOrder = &clone.PrepareOrder
	}
	if clone.HeroUpgradeGene.N > 0 {
		wrapper.HeroUpgradeGene = &clone.HeroUpgradeGene
	}
	if clone.SelectEliteRougeSkill.N > 0 {
		wrapper.SelectEliteRougeSkill = &clone.SelectEliteRougeSkill
	}
	if clone.GetStageRankInfo.N > 0 {
		wrapper.GetStageRankInfo = &clone.GetStageRankInfo
	}
	if clone.GetStageRankInfoByStageId.N > 0 {
		wrapper.GetStageRankInfoByStageId = &clone.GetStageRankInfoByStageId
	}
	if clone.CollectStageLevelRewards.N > 0 {
		wrapper.CollectStageLevelRewards = &clone.CollectStageLevelRewards
	}
	if clone.GetPhotovoltaicReward.N > 0 {
		wrapper.GetPhotovoltaicReward = &clone.GetPhotovoltaicReward
	}
	if clone.CollectPhotovoltaicReward.N > 0 {
		wrapper.CollectPhotovoltaicReward = &clone.CollectPhotovoltaicReward
	}
	if clone.SweepMainStage.N > 0 {
		wrapper.SweepMainStage = &clone.SweepMainStage
	}
	if clone.GetAllStageRankInfo.N > 0 {
		wrapper.GetAllStageRankInfo = &clone.GetAllStageRankInfo
	}
	if clone.ChangeName.N > 0 {
		wrapper.ChangeName = &clone.ChangeName
	}
	if clone.ChangeAvatar.N > 0 {
		wrapper.ChangeAvatar = &clone.ChangeAvatar
	}
	if clone.CollectMonsterBookReward.N > 0 {
		wrapper.CollectMonsterBookReward = &clone.CollectMonsterBookReward
	}
	if clone.HeroBeKilled.N > 0 {
		wrapper.HeroBeKilled = &clone.HeroBeKilled
	}
	if clone.GetDailyTask.N > 0 {
		wrapper.GetDailyTask = &clone.GetDailyTask
	}
	if clone.CollectDailyTaskReward.N > 0 {
		wrapper.CollectDailyTaskReward = &clone.CollectDailyTaskReward
	}
	if clone.CollectDailyChestReward.N > 0 {
		wrapper.CollectDailyChestReward = &clone.CollectDailyChestReward
	}
	if clone.GetMailList.N > 0 {
		wrapper.GetMailList = &clone.GetMailList
	}
	if clone.GetAllianceInfo.N > 0 {
		wrapper.GetAllianceInfo = &clone.GetAllianceInfo
	}
	if clone.CreateAlliance.N > 0 {
		wrapper.CreateAlliance = &clone.CreateAlliance
	}
	if clone.ApplyJoinAlliance.N > 0 {
		wrapper.ApplyJoinAlliance = &clone.ApplyJoinAlliance
	}
	if clone.GetAllianceMembersInfos.N > 0 {
		wrapper.GetAllianceMembersInfos = &clone.GetAllianceMembersInfos
	}
	if clone.LeaveAlliance.N > 0 {
		wrapper.LeaveAlliance = &clone.LeaveAlliance
	}
	if clone.EditAllianceName.N > 0 {
		wrapper.EditAllianceName = &clone.EditAllianceName
	}
	if clone.EditAllianceAcronym.N > 0 {
		wrapper.EditAllianceAcronym = &clone.EditAllianceAcronym
	}
	if clone.EditRecruitSetting.N > 0 {
		wrapper.EditRecruitSetting = &clone.EditRecruitSetting
	}
	if clone.EditAllianceStepName.N > 0 {
		wrapper.EditAllianceStepName = &clone.EditAllianceStepName
	}
	if clone.EditAllianceFlag.N > 0 {
		wrapper.EditAllianceFlag = &clone.EditAllianceFlag
	}
	if clone.TransferPresident.N > 0 {
		wrapper.TransferPresident = &clone.TransferPresident
	}
	if clone.RemoveMember.N > 0 {
		wrapper.RemoveMember = &clone.RemoveMember
	}
	if clone.ChangeMemberStep.N > 0 {
		wrapper.ChangeMemberStep = &clone.ChangeMemberStep
	}
	if clone.GetAllianceAppList.N > 0 {
		wrapper.GetAllianceAppList = &clone.GetAllianceAppList
	}
	if clone.HandleAllianceApp.N > 0 {
		wrapper.HandleAllianceApp = &clone.HandleAllianceApp
	}
	if clone.DisbandAlliance.N > 0 {
		wrapper.DisbandAlliance = &clone.DisbandAlliance
	}
	if clone.EditAllianceNotice.N > 0 {
		wrapper.EditAllianceNotice = &clone.EditAllianceNotice
	}
	if clone.GetAllianceList.N > 0 {
		wrapper.GetAllianceList = &clone.GetAllianceList
	}
	if clone.CancerJoinAlliance.N > 0 {
		wrapper.CancerJoinAlliance = &clone.CancerJoinAlliance
	}
	if clone.BuyAllianceShop.N > 0 {
		wrapper.BuyAllianceShop = &clone.BuyAllianceShop
	}
	if clone.CollectAllianceTaskReward.N > 0 {
		wrapper.CollectAllianceTaskReward = &clone.CollectAllianceTaskReward
	}
	if clone.CollectAllianceChestReward.N > 0 {
		wrapper.CollectAllianceChestReward = &clone.CollectAllianceChestReward
	}
	if clone.GetUserInfoList.N > 0 {
		wrapper.GetUserInfoList = &clone.GetUserInfoList
	}
	if clone.AddFriends.N > 0 {
		wrapper.AddFriends = &clone.AddFriends
	}
	if clone.DelFriends.N > 0 {
		wrapper.DelFriends = &clone.DelFriends
	}
	if clone.GetFriendsList.N > 0 {
		wrapper.GetFriendsList = &clone.GetFriendsList
	}
	if clone.AddBlackList.N > 0 {
		wrapper.AddBlackList = &clone.AddBlackList
	}
	if clone.DelBlackList.N > 0 {
		wrapper.DelBlackList = &clone.DelBlackList
	}
	if clone.GetBlackList.N > 0 {
		wrapper.GetBlackList = &clone.GetBlackList
	}
	if clone.GetFriendRecommendationList.N > 0 {
		wrapper.GetFriendRecommendationList = &clone.GetFriendRecommendationList
	}
	if clone.GetFriendAppList.N > 0 {
		wrapper.GetFriendAppList = &clone.GetFriendAppList
	}
	if clone.HandleFriendApp.N > 0 {
		wrapper.HandleFriendApp = &clone.HandleFriendApp
	}
	if clone.SettingAddFriendCondition.N > 0 {
		wrapper.SettingAddFriendCondition = &clone.SettingAddFriendCondition
	}
	if clone.HookSendMessage.N > 0 {
		wrapper.HookSendMessage = &clone.HookSendMessage
	}
	if clone.SubmitOrder.N > 0 {
		wrapper.SubmitOrder = &clone.SubmitOrder
	}
	if clone.GetPowerRankInfo.N > 0 {
		wrapper.GetPowerRankInfo = &clone.GetPowerRankInfo
	}
	if clone.UpgradeLordEquipLevel.N > 0 {
		wrapper.UpgradeLordEquipLevel = &clone.UpgradeLordEquipLevel
	}
	if clone.GemCraft.N > 0 {
		wrapper.GemCraft = &clone.GemCraft
	}
	if clone.EnhanceGem.N > 0 {
		wrapper.EnhanceGem = &clone.EnhanceGem
	}
	if clone.EquipGem.N > 0 {
		wrapper.EquipGem = &clone.EquipGem
	}
	if clone.UnEquipGem.N > 0 {
		wrapper.UnEquipGem = &clone.UnEquipGem
	}
	if clone.UpgradeLordEquipGrade.N > 0 {
		wrapper.UpgradeLordEquipGrade = &clone.UpgradeLordEquipGrade
	}
	if clone.LockGem.N > 0 {
		wrapper.LockGem = &clone.LockGem
	}
	if clone.UnlockGem.N > 0 {
		wrapper.UnlockGem = &clone.UnlockGem
	}
	if clone.SwitchEquipGem.N > 0 {
		wrapper.SwitchEquipGem = &clone.SwitchEquipGem
	}
	if clone.LordGemRandom.N > 0 {
		wrapper.LordGemRandom = &clone.LordGemRandom
	}
	if clone.HeroUpgradeQuality.N > 0 {
		wrapper.HeroUpgradeQuality = &clone.HeroUpgradeQuality
	}
	if clone.SweepDungeon.N > 0 {
		wrapper.SweepDungeon = &clone.SweepDungeon
	}
	if clone.SelectDungeonRougeTab.N > 0 {
		wrapper.SelectDungeonRougeTab = &clone.SelectDungeonRougeTab
	}
	if clone.StartDungeon.N > 0 {
		wrapper.StartDungeon = &clone.StartDungeon
	}
	if clone.RefreshDungeonRougeTab.N > 0 {
		wrapper.RefreshDungeonRougeTab = &clone.RefreshDungeonRougeTab
	}
	if clone.SaveFunctionOpen.N > 0 {
		wrapper.SaveFunctionOpen = &clone.SaveFunctionOpen
	}
	if clone.SaveGuideProgress.N > 0 {
		wrapper.SaveGuideProgress = &clone.SaveGuideProgress
	}
	if clone.GetActivityList.N > 0 {
		wrapper.GetActivityList = &clone.GetActivityList
	}
	if clone.CollectSign7Reward.N > 0 {
		wrapper.CollectSign7Reward = &clone.CollectSign7Reward
	}
	if clone.CollectDay7Reward.N > 0 {
		wrapper.CollectDay7Reward = &clone.CollectDay7Reward
	}
	if clone.CollectDay7ChestReward.N > 0 {
		wrapper.CollectDay7ChestReward = &clone.CollectDay7ChestReward
	}
	if clone.SelectDay7Reward.N > 0 {
		wrapper.SelectDay7Reward = &clone.SelectDay7Reward
	}
	if clone.CollectMailReward.N > 0 {
		wrapper.CollectMailReward = &clone.CollectMailReward
	}
	if clone.ReadMail.N > 0 {
		wrapper.ReadMail = &clone.ReadMail
	}
	if clone.DelMail.N > 0 {
		wrapper.DelMail = &clone.DelMail
	}
	if clone.DeleteAllReadMail.N > 0 {
		wrapper.DeleteAllReadMail = &clone.DeleteAllReadMail
	}
	if clone.ReadAndCollectAllMail.N > 0 {
		wrapper.ReadAndCollectAllMail = &clone.ReadAndCollectAllMail
	}
	if clone.CollectGrowthFundReward.N > 0 {
		wrapper.CollectGrowthFundReward = &clone.CollectGrowthFundReward
	}
	if clone.PushMsgs.N > 0 {
		wrapper.PushMsgs = &clone.PushMsgs
	}
	if clone.PushPBMsgs.N > 0 {
		wrapper.PushPBMsgs = &clone.PushPBMsgs
	}
	if clone.PushPBTopicMsg.N > 0 {
		wrapper.PushPBTopicMsg = &clone.PushPBTopicMsg
	}
	if clone.PushOrderFinish.N > 0 {
		wrapper.PushOrderFinish = &clone.PushOrderFinish
	}
	return &wrapper
}

func InheritStats(oldWrapper interface{}) error {
	data, err := json.Marshal(oldWrapper)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, &stats)
}
