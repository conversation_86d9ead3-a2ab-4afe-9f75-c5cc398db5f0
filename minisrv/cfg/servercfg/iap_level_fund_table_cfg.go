// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapLevelFundTableCfg struct {
	Id              int32               `json:"Id"`           // Id
	StringId        string              `json:"StringId"`     // StringId
	Unlock          int32               `json:"Unlock"`       // 通过xx关解锁
	UnlockRef       *MainLevelTableCfg  `json:"-"`            // 通过xx关解锁
	Stage           int32               `json:"Stage"`        // 阶段
	IapPackageId    int32               `json:"IapPackageId"` // 内购商品id
	IapPackageIdRef *IapPackageTableCfg `json:"-"`            // 内购商品id
	Limit           PurchaseLimitType   `json:"Limit"`        // 限购类型
	Times           int32               `json:"Times"`        // 限购次数
}

func NewIapLevelFundTableCfg() *IapLevelFundTableCfg {
	return &IapLevelFundTableCfg{
		Id:              0,
		StringId:        "",
		Unlock:          0,
		UnlockRef:       nil,
		Stage:           0,
		IapPackageId:    0,
		IapPackageIdRef: nil,
		Limit:           PurchaseLimitType(enumDefaultValue),
		Times:           0,
	}
}

type IapLevelFundTable struct {
	records  map[int32]*IapLevelFundTableCfg
	localIds map[int32]struct{}
}

func NewIapLevelFundTable() *IapLevelFundTable {
	return &IapLevelFundTable{
		records:  map[int32]*IapLevelFundTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapLevelFundTable) Get(key int32) *IapLevelFundTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapLevelFundTable) GetAll() map[int32]*IapLevelFundTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapLevelFundTable) put(key int32, value *IapLevelFundTableCfg, local bool) *IapLevelFundTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapLevelFundTable) Range(f func(v *IapLevelFundTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapLevelFundTable) Filter(filterFuncs ...func(v *IapLevelFundTableCfg) bool) map[int32]*IapLevelFundTableCfg {
	filtered := map[int32]*IapLevelFundTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapLevelFundTable) FilterSlice(filterFuncs ...func(v *IapLevelFundTableCfg) bool) []*IapLevelFundTableCfg {
	filtered := []*IapLevelFundTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapLevelFundTable) FilterKeys(filterFuncs ...func(v *IapLevelFundTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapLevelFundTable) satisfied(v *IapLevelFundTableCfg, filterFuncs ...func(v *IapLevelFundTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapLevelFundTable) setupIndexes() error {
	return nil
}

func (t *IapLevelFundTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapLevelFundTableCfg) bindRefs(c *Configs) {
	r.UnlockRef = c.MainLevelTable.Get(r.Unlock)
	r.IapPackageIdRef = c.IapPackageTable.Get(r.IapPackageId)
}
