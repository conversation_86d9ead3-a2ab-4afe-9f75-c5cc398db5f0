// Code generated by Cfgo. DO NOT EDIT.
package servercfg

type IapLevelFundRewardTableCfg struct {
	Id         int32                 `json:"Id"`         // Id
	StringId   string                `json:"StringId"`   // StringId
	Fund       int32                 `json:"Fund"`       // 所属基金
	FundRef    *IapLevelFundTableCfg `json:"-"`          // 所属基金
	Level      int32                 `json:"Level"`      // 通过关卡
	LevelRef   *MainLevelTableCfg    `json:"-"`          // 通过关卡
	RewardFree *RewardKVS            `json:"RewardFree"` // 免费奖励
	RewardVip  []*RewardKVS          `json:"RewardVip"`  // 付费奖励
}

func NewIapLevelFundRewardTableCfg() *IapLevelFundRewardTableCfg {
	return &IapLevelFundRewardTableCfg{
		Id:         0,
		StringId:   "",
		Fund:       0,
		FundRef:    nil,
		Level:      0,
		LevelRef:   nil,
		RewardFree: NewRewardKVS(),
		RewardVip:  []*RewardKVS{},
	}
}

type IapLevelFundRewardTable struct {
	records  map[int32]*IapLevelFundRewardTableCfg
	localIds map[int32]struct{}
}

func NewIapLevelFundRewardTable() *IapLevelFundRewardTable {
	return &IapLevelFundRewardTable{
		records:  map[int32]*IapLevelFundRewardTableCfg{},
		localIds: map[int32]struct{}{},
	}
}

func (t *IapLevelFundRewardTable) Get(key int32) *IapLevelFundRewardTableCfg {
	if v, ok := t.records[key]; ok {
		return v
	}
	return nil
}

func (t *IapLevelFundRewardTable) GetAll() map[int32]*IapLevelFundRewardTableCfg {
	return t.records
}

// put 添加一条数据
// local 是否本表对应的csv或者json文件中的数据
// return 如果有旧数据返回旧数据
func (t *IapLevelFundRewardTable) put(key int32, value *IapLevelFundRewardTableCfg, local bool) *IapLevelFundRewardTableCfg {
	old, ok := t.records[key]
	t.records[key] = value
	if ok {
		return old
	}
	if local {
		t.localIds[key] = struct{}{}
	}
	return nil
}

// Range 遍历全表的处理方法
// f - 遍历执行的方法，当返回值为false，则停止遍历
func (t *IapLevelFundRewardTable) Range(f func(v *IapLevelFundRewardTableCfg) bool) {
	for _, v := range t.GetAll() {
		if !f(v) {
			return
		}
	}
}

// Filter 过滤全表记录
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回map，key为表的key，value为表记录
func (t *IapLevelFundRewardTable) Filter(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) map[int32]*IapLevelFundRewardTableCfg {
	filtered := map[int32]*IapLevelFundRewardTableCfg{}
	for k, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered[k] = v
		}
	}
	return filtered
}

// FilterSlice 过滤全表记录，并返回符合条件的表记录slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 返回表记录的slice
func (t *IapLevelFundRewardTable) FilterSlice(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) []*IapLevelFundRewardTableCfg {
	filtered := []*IapLevelFundRewardTableCfg{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v)
		}
	}
	return filtered
}

// FilterKeys 过滤全表记录，并返回符合条件的表key的slice
// filterFuncs 过滤的函数集合，当所有的过滤函数都返回true，则该记录满足条件
// return 表key的slice
func (t *IapLevelFundRewardTable) FilterKeys(filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) []int32 {
	filtered := []int32{}
	for _, v := range t.records {
		if t.satisfied(v, filterFuncs...) {
			filtered = append(filtered, v.Id)
		}
	}
	return filtered
}

func (t *IapLevelFundRewardTable) satisfied(v *IapLevelFundRewardTableCfg, filterFuncs ...func(v *IapLevelFundRewardTableCfg) bool) bool {
	for _, f := range filterFuncs {
		if !f(v) {
			return false
		}
	}
	return true
}

func (t *IapLevelFundRewardTable) setupIndexes() error {
	return nil
}

func (t *IapLevelFundRewardTable) bindRefs(c *Configs) {
	for _, r := range t.records {
		r.bindRefs(c)
	}
}

func (r *IapLevelFundRewardTableCfg) bindRefs(c *Configs) {
	r.FundRef = c.IapLevelFundTable.Get(r.Fund)
	r.LevelRef = c.MainLevelTable.Get(r.Level)
	r.RewardFree.bindRefs(c)
	for _, e := range r.RewardVip {
		e.bindRefs(c)
	}
}
