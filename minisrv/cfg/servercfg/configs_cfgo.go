// Code generated by Cfgo. DO NOT EDIT.
package servercfg

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/xeipuuv/gojsonschema"
	"gitlab-ee.funplus.io/watcher/watcher/misc/json"
)

var (
	_ json.Encoder
)

func (c *Configs) GetVersionExtraInfos(k string) (string, bool) {
	v, ok := c.versionExtraInfos[k]
	return v, ok
}

func (c *Configs) checkJsonVersion(dir string, enableCheckCompatibility bool) error {
	jsonPath := filepath.Join(dir, "version.json")
	bytes, err := os.ReadFile(jsonPath)
	if err != nil {
		return fmt.Errorf("[version]please update and restart kingdom, no version.json, path=%s, err=[%w]", jsonPath, err)
	}
	versionInfoInJson := &struct {
		CfgoVersion       string
		CfgoCommitHash    string
		MetaVersion       string
		DataVersion       string
		VersionExtraInfos map[string]string
	}{}
	if err = json.Unmarshal(bytes, versionInfoInJson); err != nil {
		return fmt.Errorf("[version]please update and restart kingdom, unmarshal version.json failed, path=%s, err=[%w]", jsonPath, err)
	}
	if versionInfoInJson.CfgoVersion != c.cfgoVersion {
		return fmt.Errorf("[version]please update and restart kingdom, CfgoVersionInJson=%s, CfgoVersionInCode=%s", versionInfoInJson.CfgoVersion, c.cfgoVersion)
	}
	if len(c.metaVersion) > 0 && versionInfoInJson.MetaVersion != c.metaVersion {
		if enableCheckCompatibility {
			if err = c.checkCompatibility(dir); err != nil {
				return err
			}
		} else {
			return fmt.Errorf("[version]please update and restart kingdom, MetaVersionInJson=%s, MetaVersionInCode=%s", versionInfoInJson.MetaVersion, c.metaVersion)
		}
	}
	c.dataVersion = versionInfoInJson.DataVersion
	c.versionExtraInfos = versionInfoInJson.VersionExtraInfos
	return nil
}

var _ error = &ValidateError{}

type ValidateError struct {
	TableName string
	Errors    []gojsonschema.ResultError
}

func (e *ValidateError) Error() string {
	total := e.TableName + " validate json error: \n"
	for _, err := range e.Errors {
		total += "- " + err.String() + "\n"
	}
	return total
}

func (c *Configs) checkCompatibility(dir string) error {
	{
		jsonPath := filepath.Join(dir, "mock_data.json")
		bytes, err := os.ReadFile(jsonPath)
		if err != nil {
			return fmt.Errorf("[compatibility]please check json data, no mock_data.json, path=%s, err=[%w]", jsonPath, err)
		}
		mockData := &struct {
			Data map[string]string
		}{Data: make(map[string]string)}
		if err = json.Unmarshal(bytes, mockData); err != nil {
			return fmt.Errorf("[compatibility]please check json data, unmarshal mock_data.json failed, path=%s, err=[%w]", jsonPath, err)
		}
		var errors []error
		if err = c.ActivityTable.checkCompatibility(mockData.Data["ActivityTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaBotTable.checkCompatibility(mockData.Data["ArenaBotTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaChallengeRewardTable.checkCompatibility(mockData.Data["ArenaChallengeRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaDailyRankRewardTable.checkCompatibility(mockData.Data["ArenaDailyRankRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaExtraChallengeCntTable.checkCompatibility(mockData.Data["ArenaExtraChallengeCntTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaMatchTable.checkCompatibility(mockData.Data["ArenaMatchTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaRefreshTable.checkCompatibility(mockData.Data["ArenaRefreshTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaScoreTable.checkCompatibility(mockData.Data["ArenaScoreTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaShopTable.checkCompatibility(mockData.Data["ArenaShopTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ArenaWeeklyRankRewardTable.checkCompatibility(mockData.Data["ArenaWeeklyRankRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.AttributeHierarchyTable.checkCompatibility(mockData.Data["AttributeHierarchyTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.AvatarFrameTable.checkCompatibility(mockData.Data["AvatarFrameTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.AvatarTable.checkCompatibility(mockData.Data["AvatarTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.BattleAttributeTable.checkCompatibility(mockData.Data["BattleAttributeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.BattleModelTable.checkCompatibility(mockData.Data["BattleModelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.BenefitsCalcJustShowTable.checkCompatibility(mockData.Data["BenefitsCalcJustShowTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.BenefitsCalcTable.checkCompatibility(mockData.Data["BenefitsCalcTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.BenefitsTable.checkCompatibility(mockData.Data["BenefitsTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DailyTasksScoreTable.checkCompatibility(mockData.Data["DailyTasksScoreTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DailyTasksTable.checkCompatibility(mockData.Data["DailyTasksTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DaveLevelTable.checkCompatibility(mockData.Data["DaveLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DropGroupTable.checkCompatibility(mockData.Data["DropGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DropMainTable.checkCompatibility(mockData.Data["DropMainTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonChapterLevelTable.checkCompatibility(mockData.Data["DungeonChapterLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonCoinLevelTable.checkCompatibility(mockData.Data["DungeonCoinLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonGeneLevelTable.checkCompatibility(mockData.Data["DungeonGeneLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonLordEquipLevelTable.checkCompatibility(mockData.Data["DungeonLordEquipLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonRefreshMonsterEventTable.checkCompatibility(mockData.Data["DungeonRefreshMonsterEventTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonSunshineLevelTable.checkCompatibility(mockData.Data["DungeonSunshineLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.DungeonTypeTable.checkCompatibility(mockData.Data["DungeonTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.FunctionPreviewTable.checkCompatibility(mockData.Data["FunctionPreviewTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.FunctionTable.checkCompatibility(mockData.Data["FunctionTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GameConfigs.checkCompatibility(mockData.Data["GameConfigs"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GemAffixQualityTable.checkCompatibility(mockData.Data["GemAffixQualityTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GemQualityTypeTable.checkCompatibility(mockData.Data["GemQualityTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GoToTable.checkCompatibility(mockData.Data["GoToTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildFlagTable.checkCompatibility(mockData.Data["GuildFlagTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildHaggleTable.checkCompatibility(mockData.Data["GuildHaggleTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildLevelTable.checkCompatibility(mockData.Data["GuildLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildPermissionTable.checkCompatibility(mockData.Data["GuildPermissionTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildRankTable.checkCompatibility(mockData.Data["GuildRankTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildShopTable.checkCompatibility(mockData.Data["GuildShopTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildTaskTable.checkCompatibility(mockData.Data["GuildTaskTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.GuildTasksScoreTable.checkCompatibility(mockData.Data["GuildTasksScoreTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroBondsTable.checkCompatibility(mockData.Data["HeroBondsTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroCareerTable.checkCompatibility(mockData.Data["HeroCareerTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroConfigTable.checkCompatibility(mockData.Data["HeroConfigTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroFragmentTable.checkCompatibility(mockData.Data["HeroFragmentTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroGeneFragmentTable.checkCompatibility(mockData.Data["HeroGeneFragmentTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroGeneTable.checkCompatibility(mockData.Data["HeroGeneTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroLevelTable.checkCompatibility(mockData.Data["HeroLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroLotteryGroupTable.checkCompatibility(mockData.Data["HeroLotteryGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroLotteryMustTable.checkCompatibility(mockData.Data["HeroLotteryMustTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroLotteryRandomGroupTable.checkCompatibility(mockData.Data["HeroLotteryRandomGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroLotteryRandomTable.checkCompatibility(mockData.Data["HeroLotteryRandomTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroQualityTable.checkCompatibility(mockData.Data["HeroQualityTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroRestrainTable.checkCompatibility(mockData.Data["HeroRestrainTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillAttrTable.checkCompatibility(mockData.Data["HeroSkillAttrTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillAwakeTable.checkCompatibility(mockData.Data["HeroSkillAwakeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillBuffTable.checkCompatibility(mockData.Data["HeroSkillBuffTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillBuffTypeTable.checkCompatibility(mockData.Data["HeroSkillBuffTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillEffectTable.checkCompatibility(mockData.Data["HeroSkillEffectTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillGroupTable.checkCompatibility(mockData.Data["HeroSkillGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroSkillTypeTable.checkCompatibility(mockData.Data["HeroSkillTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroStarTable.checkCompatibility(mockData.Data["HeroStarTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroTable.checkCompatibility(mockData.Data["HeroTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.HeroTypeTable.checkCompatibility(mockData.Data["HeroTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.Iap1stTable.checkCompatibility(mockData.Data["Iap1stTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.Iap2XTable.checkCompatibility(mockData.Data["Iap2XTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapAdFreeTable.checkCompatibility(mockData.Data["IapAdFreeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapBPTable.checkCompatibility(mockData.Data["IapBPTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapBpRewardTable.checkCompatibility(mockData.Data["IapBpRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapDailySaleFreeRewardTable.checkCompatibility(mockData.Data["IapDailySaleFreeRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapDailySaleRewardGroupTable.checkCompatibility(mockData.Data["IapDailySaleRewardGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapDailySaleRewardTable.checkCompatibility(mockData.Data["IapDailySaleRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapDailySaleTable.checkCompatibility(mockData.Data["IapDailySaleTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapDealTable.checkCompatibility(mockData.Data["IapDealTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapLevelFundRewardTable.checkCompatibility(mockData.Data["IapLevelFundRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapLevelFundTable.checkCompatibility(mockData.Data["IapLevelFundTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapMonthCardTable.checkCompatibility(mockData.Data["IapMonthCardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapPackageDiamondShopTable.checkCompatibility(mockData.Data["IapPackageDiamondShopTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapPackageRewardTable.checkCompatibility(mockData.Data["IapPackageRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapPackageTable.checkCompatibility(mockData.Data["IapPackageTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapPriceTable.checkCompatibility(mockData.Data["IapPriceTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapRegularPackGroupTable.checkCompatibility(mockData.Data["IapRegularPackGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapRegularPackTable.checkCompatibility(mockData.Data["IapRegularPackTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapShopMallTable.checkCompatibility(mockData.Data["IapShopMallTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapSignRewardTable.checkCompatibility(mockData.Data["IapSignRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapSignTable.checkCompatibility(mockData.Data["IapSignTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapTriggerPackGroupTable.checkCompatibility(mockData.Data["IapTriggerPackGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapTriggerPackTable.checkCompatibility(mockData.Data["IapTriggerPackTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IapTurnPackTable.checkCompatibility(mockData.Data["IapTurnPackTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IdleMonsterTable.checkCompatibility(mockData.Data["IdleMonsterTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IdleRewardTable.checkCompatibility(mockData.Data["IdleRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.IdleRewardTime.checkCompatibility(mockData.Data["IdleRewardTime"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ItemQualityTable.checkCompatibility(mockData.Data["ItemQualityTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ItemSourceTable.checkCompatibility(mockData.Data["ItemSourceTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ItemTable.checkCompatibility(mockData.Data["ItemTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LanguageCnTable.checkCompatibility(mockData.Data["LanguageCnTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LoginOpenTable.checkCompatibility(mockData.Data["LoginOpenTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordEquipGradeTable.checkCompatibility(mockData.Data["LordEquipGradeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordEquipGradeTypeTable.checkCompatibility(mockData.Data["LordEquipGradeTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordEquipTable.checkCompatibility(mockData.Data["LordEquipTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordEquipTypeTable.checkCompatibility(mockData.Data["LordEquipTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemCraftTable.checkCompatibility(mockData.Data["LordGemCraftTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemDropCntTable.checkCompatibility(mockData.Data["LordGemDropCntTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemDropQualityTable.checkCompatibility(mockData.Data["LordGemDropQualityTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemRandomGroupChanceTable.checkCompatibility(mockData.Data["LordGemRandomGroupChanceTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemRandomGroupMustTable.checkCompatibility(mockData.Data["LordGemRandomGroupMustTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemRandomGroupTable.checkCompatibility(mockData.Data["LordGemRandomGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemRandomRewardGroupTable.checkCompatibility(mockData.Data["LordGemRandomRewardGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemReforgeTable.checkCompatibility(mockData.Data["LordGemReforgeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.LordGemTable.checkCompatibility(mockData.Data["LordGemTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MailTable.checkCompatibility(mockData.Data["MailTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainChapterLevelTable.checkCompatibility(mockData.Data["MainChapterLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainChapterTable.checkCompatibility(mockData.Data["MainChapterTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelPassRewardTable.checkCompatibility(mockData.Data["MainLevelPassRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelRangeDmgTable.checkCompatibility(mockData.Data["MainLevelRangeDmgTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelRewardRatioTable.checkCompatibility(mockData.Data["MainLevelRewardRatioTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelRewardTable.checkCompatibility(mockData.Data["MainLevelRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelRogueRewardWeightTable.checkCompatibility(mockData.Data["MainLevelRogueRewardWeightTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLevelTable.checkCompatibility(mockData.Data["MainLevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MainLineTasksTable.checkCompatibility(mockData.Data["MainLineTasksTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventBuffTable.checkCompatibility(mockData.Data["MapEventBuffTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventMonsterGroupTable.checkCompatibility(mockData.Data["MapEventMonsterGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventMonsterTable.checkCompatibility(mockData.Data["MapEventMonsterTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventObstacleTable.checkCompatibility(mockData.Data["MapEventObstacleTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventPropTable.checkCompatibility(mockData.Data["MapEventPropTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventRewardTable.checkCompatibility(mockData.Data["MapEventRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventSkillTable.checkCompatibility(mockData.Data["MapEventSkillTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapEventTable.checkCompatibility(mockData.Data["MapEventTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MapRefreshMonsterEventTable.checkCompatibility(mockData.Data["MapRefreshMonsterEventTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ModifierTable.checkCompatibility(mockData.Data["ModifierTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterCareerTable.checkCompatibility(mockData.Data["MonsterCareerTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterGradeTable.checkCompatibility(mockData.Data["MonsterGradeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterPosTypeTable.checkCompatibility(mockData.Data["MonsterPosTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterSkillTable.checkCompatibility(mockData.Data["MonsterSkillTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterTable.checkCompatibility(mockData.Data["MonsterTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.MonsterTypeTable.checkCompatibility(mockData.Data["MonsterTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.NewbieTable.checkCompatibility(mockData.Data["NewbieTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.NpcDialogueTable.checkCompatibility(mockData.Data["NpcDialogueTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.PhotovoltaicTable.checkCompatibility(mockData.Data["PhotovoltaicTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.PresetsTable.checkCompatibility(mockData.Data["PresetsTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RankMainTable.checkCompatibility(mockData.Data["RankMainTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RankRewardTable.checkCompatibility(mockData.Data["RankRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeRefreshTable.checkCompatibility(mockData.Data["RougeRefreshTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeTabEffectTable.checkCompatibility(mockData.Data["RougeTabEffectTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeTabGroupRandomTable.checkCompatibility(mockData.Data["RougeTabGroupRandomTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeTabGroupTable.checkCompatibility(mockData.Data["RougeTabGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeTabNewbieTable.checkCompatibility(mockData.Data["RougeTabNewbieTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeTabTable.checkCompatibility(mockData.Data["RougeTabTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.RougeWeightCoef.checkCompatibility(mockData.Data["RougeWeightCoef"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.SelectChestGroupTable.checkCompatibility(mockData.Data["SelectChestGroupTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.SelectChestMainTable.checkCompatibility(mockData.Data["SelectChestMainTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.SevenDayTasksScoreTable.checkCompatibility(mockData.Data["SevenDayTasksScoreTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.SevenDayTasksTable.checkCompatibility(mockData.Data["SevenDayTasksTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.ShopTable.checkCompatibility(mockData.Data["ShopTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.Sign7Table.checkCompatibility(mockData.Data["Sign7Table"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.SkillDmgTypeTable.checkCompatibility(mockData.Data["SkillDmgTypeTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TowerAILevelTable.checkCompatibility(mockData.Data["TowerAILevelTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TowerAITable.checkCompatibility(mockData.Data["TowerAITable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TowerTable.checkCompatibility(mockData.Data["TowerTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TurnRewardTable.checkCompatibility(mockData.Data["TurnRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TurnScoreRewardTable.checkCompatibility(mockData.Data["TurnScoreRewardTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.TurnTable.checkCompatibility(mockData.Data["TurnTable"]); err != nil {
			errors = append(errors, err)
		}
		if err = c.VehicleTable.checkCompatibility(mockData.Data["VehicleTable"]); err != nil {
			errors = append(errors, err)
		}

		if len(errors) > 0 {
			return fmt.Errorf("[compatibility]compatibility error, errs: %v", errors)
		}
	}
	{
		enumsInCode := make(map[string]map[string]int)
		{
			enumsInCode["AttrDefaultType"] = make(map[string]int)
			enumsInCode["AttrDefaultType"]["Number"] = 1
			enumsInCode["AttrDefaultType"]["Model"] = 2
			enumsInCode["AttrDefaultType"]["Buff"] = 3
			enumsInCode["AttrDefaultType"]["Effect"] = 4

			enumsInCode["BenefitCalcFormula"] = make(map[string]int)
			enumsInCode["BenefitCalcFormula"]["Formula1"] = 1
			enumsInCode["BenefitCalcFormula"]["Formula2"] = 2
			enumsInCode["BenefitCalcFormula"]["Formula3"] = 3
			enumsInCode["BenefitCalcFormula"]["Formula4"] = 4
			enumsInCode["BenefitCalcFormula"]["Formula5"] = 5
			enumsInCode["BenefitCalcFormula"]["Formula6"] = 6
			enumsInCode["BenefitCalcFormula"]["Formula7"] = 7
			enumsInCode["BenefitCalcFormula"]["Formula8"] = 8
			enumsInCode["BenefitCalcFormula"]["Formula9"] = 9

			enumsInCode["BuffOverlyingType"] = make(map[string]int)
			enumsInCode["BuffOverlyingType"]["NoOverlying"] = 1
			enumsInCode["BuffOverlyingType"]["TimeOverlying"] = 2
			enumsInCode["BuffOverlyingType"]["EffectOverlying"] = 3
			enumsInCode["BuffOverlyingType"]["EffectCover"] = 4
			enumsInCode["BuffOverlyingType"]["EffectReplace"] = 5
			enumsInCode["BuffOverlyingType"]["EffectMultiple"] = 6

			enumsInCode["BuffTarget"] = make(map[string]int)
			enumsInCode["BuffTarget"]["Own"] = 1
			enumsInCode["BuffTarget"]["OwnSide"] = 2
			enumsInCode["BuffTarget"]["OwnSideHeroDefense"] = 3
			enumsInCode["BuffTarget"]["OwnSideHeroRanged"] = 4
			enumsInCode["BuffTarget"]["OwnSideHeroSupport"] = 5
			enumsInCode["BuffTarget"]["OwnSideHeroFront"] = 6
			enumsInCode["BuffTarget"]["OwnSideHeroBehind"] = 7
			enumsInCode["BuffTarget"]["OwnSideHeroHPLowest"] = 8
			enumsInCode["BuffTarget"]["Opposite"] = 9
			enumsInCode["BuffTarget"]["OppositeSide"] = 10
			enumsInCode["BuffTarget"]["OppositeSideHeroDefense"] = 11
			enumsInCode["BuffTarget"]["OppositeSideHeroRanged"] = 12
			enumsInCode["BuffTarget"]["OppositeSideHeroSupport"] = 13
			enumsInCode["BuffTarget"]["OppoSideHeroFront"] = 14
			enumsInCode["BuffTarget"]["OppoSideHeroBehind"] = 15
			enumsInCode["BuffTarget"]["OppositeSideHeroHpLowest"] = 16
			enumsInCode["BuffTarget"]["PreSkillEffectTarget"] = 17
			enumsInCode["BuffTarget"]["PreSkillEffectTargetElseBoss"] = 18
			enumsInCode["BuffTarget"]["Boss"] = 19
			enumsInCode["BuffTarget"]["Vehicle"] = 20
			enumsInCode["BuffTarget"]["OwnForward"] = 21
			enumsInCode["BuffTarget"]["OwnSideHeroMagic"] = 22
			enumsInCode["BuffTarget"]["OwnSideHeroSuperPowers"] = 23
			enumsInCode["BuffTarget"]["OwnSideHeroTech"] = 24
			enumsInCode["BuffTarget"]["OppositeSideHeroMagic"] = 25
			enumsInCode["BuffTarget"]["OppositeSideHeroSuperPowers"] = 26
			enumsInCode["BuffTarget"]["OppositeSideHeroTech"] = 27
			enumsInCode["BuffTarget"]["OwnSideHeroAtkHighest"] = 28
			enumsInCode["BuffTarget"]["OwnSideHeroBehindMagic"] = 29
			enumsInCode["BuffTarget"]["OwnSideHeroBehindTech"] = 30
			enumsInCode["BuffTarget"]["OwnSideHeroFrontDefenseRandom"] = 31

			enumsInCode["BuffType"] = make(map[string]int)
			enumsInCode["BuffType"]["Buff"] = 1
			enumsInCode["BuffType"]["Debuff"] = 2

			enumsInCode["CorrectType"] = make(map[string]int)
			enumsInCode["CorrectType"]["Overlying"] = 1
			enumsInCode["CorrectType"]["Cover"] = 2

			enumsInCode["DungeonType"] = make(map[string]int)
			enumsInCode["DungeonType"]["CoinDungeon"] = 1
			enumsInCode["DungeonType"]["GeneDungeon"] = 2
			enumsInCode["DungeonType"]["LordEquipDungeon"] = 3
			enumsInCode["DungeonType"]["SunshineDungeon"] = 4

			enumsInCode["GemAffixQuality"] = make(map[string]int)
			enumsInCode["GemAffixQuality"]["GemAffixQuality1"] = 1
			enumsInCode["GemAffixQuality"]["GemAffixQuality2"] = 2
			enumsInCode["GemAffixQuality"]["GemAffixQuality3"] = 3

			enumsInCode["GemQualityType"] = make(map[string]int)
			enumsInCode["GemQualityType"]["GemQualityType1"] = 1
			enumsInCode["GemQualityType"]["GemQualityType2"] = 2
			enumsInCode["GemQualityType"]["GemQualityType3"] = 3
			enumsInCode["GemQualityType"]["GemQualityType4"] = 4
			enumsInCode["GemQualityType"]["GemQualityType5"] = 5
			enumsInCode["GemQualityType"]["GemQualityType6"] = 6
			enumsInCode["GemQualityType"]["GemQualityType7"] = 7

			enumsInCode["GuildFlagType"] = make(map[string]int)
			enumsInCode["GuildFlagType"]["Base"] = 1
			enumsInCode["GuildFlagType"]["Badge"] = 2

			enumsInCode["GuildPermission"] = make(map[string]int)
			enumsInCode["GuildPermission"]["ChangeGuildFlag"] = 1
			enumsInCode["GuildPermission"]["ChangeGuildShortName"] = 2
			enumsInCode["GuildPermission"]["ChangeGuildName"] = 3
			enumsInCode["GuildPermission"]["EditNotice"] = 4
			enumsInCode["GuildPermission"]["ChangeRecruitSetting"] = 5
			enumsInCode["GuildPermission"]["ManageJoinApplication"] = 6
			enumsInCode["GuildPermission"]["DisbandGuild"] = 7
			enumsInCode["GuildPermission"]["TransferPresident"] = 8
			enumsInCode["GuildPermission"]["RemoveMember"] = 9
			enumsInCode["GuildPermission"]["ChangeMemberRank"] = 10
			enumsInCode["GuildPermission"]["ViewMemberInfo"] = 11
			enumsInCode["GuildPermission"]["ChangeRankTitle"] = 12
			enumsInCode["GuildPermission"]["ChangeLanguage"] = 13
			enumsInCode["GuildPermission"]["ExitGuild"] = 14

			enumsInCode["GuildRank"] = make(map[string]int)
			enumsInCode["GuildRank"]["Rank5"] = 1
			enumsInCode["GuildRank"]["Rank4"] = 2
			enumsInCode["GuildRank"]["Rank3"] = 3
			enumsInCode["GuildRank"]["Rank2"] = 4
			enumsInCode["GuildRank"]["Rank1"] = 5

			enumsInCode["HeroCareer"] = make(map[string]int)
			enumsInCode["HeroCareer"]["HeroDefense"] = 1
			enumsInCode["HeroCareer"]["HeroRanged"] = 2
			enumsInCode["HeroCareer"]["HeroSupport"] = 3

			enumsInCode["HeroConfig"] = make(map[string]int)
			enumsInCode["HeroConfig"]["HeroConfig1"] = 1
			enumsInCode["HeroConfig"]["HeroConfig2"] = 2
			enumsInCode["HeroConfig"]["HeroConfig3"] = 3
			enumsInCode["HeroConfig"]["HeroConfig4"] = 4
			enumsInCode["HeroConfig"]["HeroConfig5"] = 5

			enumsInCode["HeroQuality"] = make(map[string]int)
			enumsInCode["HeroQuality"]["HeroLegendary"] = 1
			enumsInCode["HeroQuality"]["HeroEpic"] = 2
			enumsInCode["HeroQuality"]["HeroRare"] = 3

			enumsInCode["HeroSkillBuffType"] = make(map[string]int)
			enumsInCode["HeroSkillBuffType"]["Benefit"] = 1
			enumsInCode["HeroSkillBuffType"]["Silent"] = 2
			enumsInCode["HeroSkillBuffType"]["Stun"] = 3
			enumsInCode["HeroSkillBuffType"]["Paralysis"] = 4
			enumsInCode["HeroSkillBuffType"]["Sleep"] = 5
			enumsInCode["HeroSkillBuffType"]["Bind"] = 6
			enumsInCode["HeroSkillBuffType"]["Immortal"] = 7
			enumsInCode["HeroSkillBuffType"]["Veil"] = 8
			enumsInCode["HeroSkillBuffType"]["Stealth"] = 9
			enumsInCode["HeroSkillBuffType"]["Curse"] = 10
			enumsInCode["HeroSkillBuffType"]["Dot_bleed"] = 11
			enumsInCode["HeroSkillBuffType"]["Dot_poison"] = 12
			enumsInCode["HeroSkillBuffType"]["Dot_frostbite"] = 13
			enumsInCode["HeroSkillBuffType"]["Dot_burn"] = 14
			enumsInCode["HeroSkillBuffType"]["Block"] = 15
			enumsInCode["HeroSkillBuffType"]["Unrevive"] = 16
			enumsInCode["HeroSkillBuffType"]["EternalSlumber"] = 17
			enumsInCode["HeroSkillBuffType"]["Tense"] = 18
			enumsInCode["HeroSkillBuffType"]["Immunity"] = 19
			enumsInCode["HeroSkillBuffType"]["Shield"] = 20
			enumsInCode["HeroSkillBuffType"]["HalfAsleep"] = 21
			enumsInCode["HeroSkillBuffType"]["Nightmare"] = 22
			enumsInCode["HeroSkillBuffType"]["LifeSteal"] = 23
			enumsInCode["HeroSkillBuffType"]["Revive"] = 24
			enumsInCode["HeroSkillBuffType"]["HpRecovery"] = 25
			enumsInCode["HeroSkillBuffType"]["SkillSwitch"] = 26
			enumsInCode["HeroSkillBuffType"]["Taunted"] = 27
			enumsInCode["HeroSkillBuffType"]["Dmg"] = 28
			enumsInCode["HeroSkillBuffType"]["RemoveBuff"] = 29
			enumsInCode["HeroSkillBuffType"]["OnSideExplosion"] = 30
			enumsInCode["HeroSkillBuffType"]["Frozen"] = 31
			enumsInCode["HeroSkillBuffType"]["Repel"] = 32
			enumsInCode["HeroSkillBuffType"]["Pull"] = 33
			enumsInCode["HeroSkillBuffType"]["LightingStruck"] = 34
			enumsInCode["HeroSkillBuffType"]["Vulnerability"] = 35
			enumsInCode["HeroSkillBuffType"]["Lame"] = 36
			enumsInCode["HeroSkillBuffType"]["Rampage"] = 37
			enumsInCode["HeroSkillBuffType"]["BuffDelay"] = 38
			enumsInCode["HeroSkillBuffType"]["CoolingOff"] = 39
			enumsInCode["HeroSkillBuffType"]["RecoveryDown"] = 40
			enumsInCode["HeroSkillBuffType"]["Armour"] = 41
			enumsInCode["HeroSkillBuffType"]["FrozenHpRecovery"] = 42
			enumsInCode["HeroSkillBuffType"]["ElectrostaticSputtering"] = 43
			enumsInCode["HeroSkillBuffType"]["InjuryHealing"] = 44
			enumsInCode["HeroSkillBuffType"]["Summon"] = 45
			enumsInCode["HeroSkillBuffType"]["CrabWalk"] = 46
			enumsInCode["HeroSkillBuffType"]["Invulnerable"] = 47
			enumsInCode["HeroSkillBuffType"]["Split"] = 48
			enumsInCode["HeroSkillBuffType"]["Trigger"] = 49
			enumsInCode["HeroSkillBuffType"]["Excavation"] = 50
			enumsInCode["HeroSkillBuffType"]["TombStone"] = 51
			enumsInCode["HeroSkillBuffType"]["InstantDeath"] = 52
			enumsInCode["HeroSkillBuffType"]["RangeRampage"] = 53
			enumsInCode["HeroSkillBuffType"]["Dot_wind"] = 54
			enumsInCode["HeroSkillBuffType"]["ConditionTrigger"] = 55
			enumsInCode["HeroSkillBuffType"]["Immolate"] = 56
			enumsInCode["HeroSkillBuffType"]["HpSwitch"] = 57
			enumsInCode["HeroSkillBuffType"]["StepingStone"] = 58
			enumsInCode["HeroSkillBuffType"]["EDER"] = 59
			enumsInCode["HeroSkillBuffType"]["Shippuden"] = 60

			enumsInCode["HeroSkillDmgType"] = make(map[string]int)
			enumsInCode["HeroSkillDmgType"]["DmgValue"] = 1
			enumsInCode["HeroSkillDmgType"]["DmgRatio"] = 2
			enumsInCode["HeroSkillDmgType"]["MaxHpPerDmg"] = 3
			enumsInCode["HeroSkillDmgType"]["CurHpPerDmg"] = 4
			enumsInCode["HeroSkillDmgType"]["LossHpPerDmg"] = 5
			enumsInCode["HeroSkillDmgType"]["InheritedSkillRatio"] = 6

			enumsInCode["HeroSkillEffectType"] = make(map[string]int)
			enumsInCode["HeroSkillEffectType"]["Buff"] = 1
			enumsInCode["HeroSkillEffectType"]["GuideLaser"] = 2
			enumsInCode["HeroSkillEffectType"]["BulletFire"] = 3
			enumsInCode["HeroSkillEffectType"]["IceRose"] = 4
			enumsInCode["HeroSkillEffectType"]["BulletWind"] = 5
			enumsInCode["HeroSkillEffectType"]["Laser"] = 6
			enumsInCode["HeroSkillEffectType"]["ElectricFierce"] = 7
			enumsInCode["HeroSkillEffectType"]["Airdrop"] = 8
			enumsInCode["HeroSkillEffectType"]["ElectricArc"] = 9
			enumsInCode["HeroSkillEffectType"]["BulletIce"] = 10
			enumsInCode["HeroSkillEffectType"]["DragonFlame"] = 11
			enumsInCode["HeroSkillEffectType"]["Car"] = 12
			enumsInCode["HeroSkillEffectType"]["Shrapnel"] = 13
			enumsInCode["HeroSkillEffectType"]["Cyclone"] = 14
			enumsInCode["HeroSkillEffectType"]["BulletPea"] = 15
			enumsInCode["HeroSkillEffectType"]["HandSword"] = 16
			enumsInCode["HeroSkillEffectType"]["Missile"] = 17
			enumsInCode["HeroSkillEffectType"]["MeleeAtk"] = 18
			enumsInCode["HeroSkillEffectType"]["SummonMonster"] = 19
			enumsInCode["HeroSkillEffectType"]["Aoe"] = 20
			enumsInCode["HeroSkillEffectType"]["UnlockTabLevelLimit"] = 21
			enumsInCode["HeroSkillEffectType"]["GetTab"] = 22
			enumsInCode["HeroSkillEffectType"]["UnlockTab"] = 23
			enumsInCode["HeroSkillEffectType"]["HomelanderLaser"] = 24
			enumsInCode["HeroSkillEffectType"]["ReplacedTab"] = 25
			enumsInCode["HeroSkillEffectType"]["BossEarLaser"] = 26
			enumsInCode["HeroSkillEffectType"]["BossMouseLaser"] = 27
			enumsInCode["HeroSkillEffectType"]["Boss2"] = 28
			enumsInCode["HeroSkillEffectType"]["MultipleMeleeAtk"] = 29
			enumsInCode["HeroSkillEffectType"]["SuicideBombing"] = 30
			enumsInCode["HeroSkillEffectType"]["Phase2Boss1"] = 31
			enumsInCode["HeroSkillEffectType"]["Phase2Boss6"] = 32

			enumsInCode["HeroSkillHpRecoveryType"] = make(map[string]int)
			enumsInCode["HeroSkillHpRecoveryType"]["HpRecoveryValue"] = 1
			enumsInCode["HeroSkillHpRecoveryType"]["MaxHpPerRecovery"] = 2
			enumsInCode["HeroSkillHpRecoveryType"]["CurHpPerRecovery"] = 3
			enumsInCode["HeroSkillHpRecoveryType"]["LossHpPerRecovery"] = 4
			enumsInCode["HeroSkillHpRecoveryType"]["DmgPerRecovery"] = 5

			enumsInCode["HeroSkillRangePolygon"] = make(map[string]int)
			enumsInCode["HeroSkillRangePolygon"]["Rectangle"] = 1
			enumsInCode["HeroSkillRangePolygon"]["Circular"] = 2
			enumsInCode["HeroSkillRangePolygon"]["Sector"] = 3

			enumsInCode["HeroSkillType"] = make(map[string]int)
			enumsInCode["HeroSkillType"]["HitSkill"] = 1
			enumsInCode["HeroSkillType"]["NegativeSkill"] = 2
			enumsInCode["HeroSkillType"]["GiftSkill"] = 3

			enumsInCode["HeroType"] = make(map[string]int)
			enumsInCode["HeroType"]["Magic"] = 1
			enumsInCode["HeroType"]["SuperPowers"] = 2
			enumsInCode["HeroType"]["Tech"] = 3

			enumsInCode["IapBoothType"] = make(map[string]int)
			enumsInCode["IapBoothType"]["DiamondShop"] = 1
			enumsInCode["IapBoothType"]["DailySale"] = 2
			enumsInCode["IapBoothType"]["RegularPack"] = 3
			enumsInCode["IapBoothType"]["RegularBp"] = 4
			enumsInCode["IapBoothType"]["NoAds"] = 5
			enumsInCode["IapBoothType"]["2X"] = 6
			enumsInCode["IapBoothType"]["MonthCard"] = 7
			enumsInCode["IapBoothType"]["Fund"] = 8
			enumsInCode["IapBoothType"]["Sign"] = 9
			enumsInCode["IapBoothType"]["TurnTable"] = 10
			enumsInCode["IapBoothType"]["Sign7"] = 11

			enumsInCode["IapPackageType"] = make(map[string]int)
			enumsInCode["IapPackageType"]["Diamond"] = 1
			enumsInCode["IapPackageType"]["First"] = 2
			enumsInCode["IapPackageType"]["MonthCard"] = 3
			enumsInCode["IapPackageType"]["Fund"] = 4
			enumsInCode["IapPackageType"]["Regular"] = 5
			enumsInCode["IapPackageType"]["DailySale"] = 6
			enumsInCode["IapPackageType"]["AdFree"] = 7
			enumsInCode["IapPackageType"]["Life"] = 8
			enumsInCode["IapPackageType"]["SignBp"] = 9
			enumsInCode["IapPackageType"]["TurnActivity"] = 10
			enumsInCode["IapPackageType"]["Trigger"] = 11

			enumsInCode["ItemType"] = make(map[string]int)
			enumsInCode["ItemType"]["Chest"] = 1
			enumsInCode["ItemType"]["ChestSelfSelect"] = 2
			enumsInCode["ItemType"]["Diamond"] = 3
			enumsInCode["ItemType"]["Doughnut"] = 4
			enumsInCode["ItemType"]["SunShine"] = 5
			enumsInCode["ItemType"]["SummonCard"] = 6
			enumsInCode["ItemType"]["SeedBagCommon"] = 7
			enumsInCode["ItemType"]["SeedBagRare"] = 8
			enumsInCode["ItemType"]["SeedBagEpic"] = 9
			enumsInCode["ItemType"]["SeedBagLegendary"] = 10
			enumsInCode["ItemType"]["SeedBagMyth"] = 11
			enumsInCode["ItemType"]["LegendarySkillBook"] = 12
			enumsInCode["ItemType"]["EpicSkillBook"] = 13
			enumsInCode["ItemType"]["RareSkillBook"] = 14
			enumsInCode["ItemType"]["UniversalLegendaryHeroFragment"] = 15
			enumsInCode["ItemType"]["UniversalEpicHeroFragment"] = 16
			enumsInCode["ItemType"]["UniversalRareHeroFragment"] = 17
			enumsInCode["ItemType"]["RandomLegendaryHeroFragment"] = 18
			enumsInCode["ItemType"]["RandomEpicHeroFragment"] = 19
			enumsInCode["ItemType"]["RandomRareHeroFragment"] = 20
			enumsInCode["ItemType"]["Hero"] = 21
			enumsInCode["ItemType"]["HeroFragment"] = 22
			enumsInCode["ItemType"]["Avatar"] = 23
			enumsInCode["ItemType"]["AvatarFrame"] = 24
			enumsInCode["ItemType"]["RougeExp"] = 25
			enumsInCode["ItemType"]["SkillBook"] = 26
			enumsInCode["ItemType"]["Energy"] = 27
			enumsInCode["ItemType"]["HeroGeneFragment"] = 28
			enumsInCode["ItemType"]["Coin"] = 29
			enumsInCode["ItemType"]["HeroGeneralGeneFragment"] = 30
			enumsInCode["ItemType"]["GemRandom"] = 31
			enumsInCode["ItemType"]["Gem"] = 32
			enumsInCode["ItemType"]["GemReforge"] = 33
			enumsInCode["ItemType"]["LordEquipManual"] = 34
			enumsInCode["ItemType"]["LordEquipRandomManual"] = 35
			enumsInCode["ItemType"]["GemDraw"] = 36
			enumsInCode["ItemType"]["GuildExp"] = 37
			enumsInCode["ItemType"]["GuildCoin"] = 38
			enumsInCode["ItemType"]["ArenaCoin"] = 39
			enumsInCode["ItemType"]["TowerKey"] = 40
			enumsInCode["ItemType"]["CoinDungeonKey"] = 41
			enumsInCode["ItemType"]["GeneDungeonKey"] = 42
			enumsInCode["ItemType"]["LordEquipDungenKey"] = 43
			enumsInCode["ItemType"]["LordEquipGradeUpManual"] = 44
			enumsInCode["ItemType"]["RegularBpKey1"] = 45
			enumsInCode["ItemType"]["FreeAd"] = 46
			enumsInCode["ItemType"]["2X"] = 47
			enumsInCode["ItemType"]["SignKey1"] = 48
			enumsInCode["ItemType"]["FundKey1"] = 49
			enumsInCode["ItemType"]["MonthCard1"] = 50
			enumsInCode["ItemType"]["MonthCard2"] = 51
			enumsInCode["ItemType"]["turntablecoin"] = 52
			enumsInCode["ItemType"]["LordEquipMaterial"] = 53
			enumsInCode["ItemType"]["HeroQualityUp"] = 54
			enumsInCode["ItemType"]["SevenDayTasksScore"] = 55

			enumsInCode["LaserTarget"] = make(map[string]int)
			enumsInCode["LaserTarget"]["Opposite"] = 1
			enumsInCode["LaserTarget"]["OppositeSideHeroDefense"] = 2
			enumsInCode["LaserTarget"]["OppositeSideHeroRanged"] = 3
			enumsInCode["LaserTarget"]["OppositeSideHeroSupport"] = 4
			enumsInCode["LaserTarget"]["OppoSideHeroFront"] = 5
			enumsInCode["LaserTarget"]["OppoSideHeroBehind"] = 6
			enumsInCode["LaserTarget"]["OppositeSideHeroHpLowest"] = 7
			enumsInCode["LaserTarget"]["PreSkillEffectTarget"] = 8
			enumsInCode["LaserTarget"]["PreSkillEffectTargetElseBoss"] = 9
			enumsInCode["LaserTarget"]["Boss"] = 10
			enumsInCode["LaserTarget"]["OwnForward"] = 11

			enumsInCode["LevelType"] = make(map[string]int)
			enumsInCode["LevelType"]["TowerDefense"] = 1
			enumsInCode["LevelType"]["ParkOur"] = 2

			enumsInCode["LogicType"] = make(map[string]int)
			enumsInCode["LogicType"]["And"] = 1
			enumsInCode["LogicType"]["Or"] = 2
			enumsInCode["LogicType"]["Invert"] = 3

			enumsInCode["LordEquipGradeType"] = make(map[string]int)
			enumsInCode["LordEquipGradeType"]["LordEquipGrade1"] = 1
			enumsInCode["LordEquipGradeType"]["LordEquipGrade2"] = 2
			enumsInCode["LordEquipGradeType"]["LordEquipGrade3"] = 3
			enumsInCode["LordEquipGradeType"]["LordEquipGrade4"] = 4
			enumsInCode["LordEquipGradeType"]["LordEquipGrade5"] = 5
			enumsInCode["LordEquipGradeType"]["LordEquipGrade6"] = 6
			enumsInCode["LordEquipGradeType"]["LordEquipGrade7"] = 7

			enumsInCode["LordEquipType"] = make(map[string]int)
			enumsInCode["LordEquipType"]["LordEquipType1"] = 1
			enumsInCode["LordEquipType"]["LordEquipType2"] = 2
			enumsInCode["LordEquipType"]["LordEquipType3"] = 3
			enumsInCode["LordEquipType"]["LordEquipType4"] = 4
			enumsInCode["LordEquipType"]["LordEquipType5"] = 5
			enumsInCode["LordEquipType"]["LordEquipType6"] = 6

			enumsInCode["MapEventType"] = make(map[string]int)
			enumsInCode["MapEventType"]["Monster"] = 1
			enumsInCode["MapEventType"]["Prop"] = 2
			enumsInCode["MapEventType"]["Buff"] = 3
			enumsInCode["MapEventType"]["Obstacle"] = 4
			enumsInCode["MapEventType"]["Reward"] = 5

			enumsInCode["MissileTarget"] = make(map[string]int)
			enumsInCode["MissileTarget"]["Opposite"] = 1
			enumsInCode["MissileTarget"]["OppositeSideHeroDefense"] = 2
			enumsInCode["MissileTarget"]["OppositeSideHeroRanged"] = 3
			enumsInCode["MissileTarget"]["OppositeSideHeroSupport"] = 4
			enumsInCode["MissileTarget"]["OppoSideHeroFront"] = 5
			enumsInCode["MissileTarget"]["OppoSideHeroBehind"] = 6
			enumsInCode["MissileTarget"]["OppositeSideHeroHpLowest"] = 7
			enumsInCode["MissileTarget"]["PreSkillEffectTarget"] = 8
			enumsInCode["MissileTarget"]["PreSkillEffectTargetElseBoss"] = 9
			enumsInCode["MissileTarget"]["Boss"] = 10

			enumsInCode["MonsterCareerType"] = make(map[string]int)
			enumsInCode["MonsterCareerType"]["Melee"] = 1
			enumsInCode["MonsterCareerType"]["Ranger"] = 2
			enumsInCode["MonsterCareerType"]["Tank"] = 3
			enumsInCode["MonsterCareerType"]["Assassin"] = 4
			enumsInCode["MonsterCareerType"]["AirMelee"] = 5
			enumsInCode["MonsterCareerType"]["AirRanger"] = 6
			enumsInCode["MonsterCareerType"]["Suicide"] = 7
			enumsInCode["MonsterCareerType"]["LongRanger"] = 8

			enumsInCode["MonsterGrade"] = make(map[string]int)
			enumsInCode["MonsterGrade"]["Common"] = 1
			enumsInCode["MonsterGrade"]["Elite"] = 2
			enumsInCode["MonsterGrade"]["Boss"] = 3

			enumsInCode["MonsterPosType"] = make(map[string]int)
			enumsInCode["MonsterPosType"]["Ground"] = 1
			enumsInCode["MonsterPosType"]["Air"] = 2

			enumsInCode["MonsterRefreshType"] = make(map[string]int)
			enumsInCode["MonsterRefreshType"]["InitialRefresh"] = 1
			enumsInCode["MonsterRefreshType"]["DelayRefresh"] = 2
			enumsInCode["MonsterRefreshType"]["UpstreamDeathCntRefresh"] = 3
			enumsInCode["MonsterRefreshType"]["PassPlotsRefresh"] = 4
			enumsInCode["MonsterRefreshType"]["AfterRefreshing"] = 5
			enumsInCode["MonsterRefreshType"]["AfterTheGameStarts"] = 6

			enumsInCode["PurchaseLimitType"] = make(map[string]int)
			enumsInCode["PurchaseLimitType"]["DailyLimit"] = 1
			enumsInCode["PurchaseLimitType"]["WeeklyLimit"] = 2
			enumsInCode["PurchaseLimitType"]["MonthlyLimit"] = 3
			enumsInCode["PurchaseLimitType"]["LifeLimit"] = 4
			enumsInCode["PurchaseLimitType"]["UnLimit"] = 5

			enumsInCode["RougeTabType"] = make(map[string]int)
			enumsInCode["RougeTabType"]["EffectTab"] = 1
			enumsInCode["RougeTabType"]["UnlockTab"] = 2
			enumsInCode["RougeTabType"]["ConfigTab"] = 3

			enumsInCode["ShopType"] = make(map[string]int)
			enumsInCode["ShopType"]["GuildShop"] = 1
			enumsInCode["ShopType"]["ArenaShop"] = 2

			enumsInCode["SkillAttrOverlyingType"] = make(map[string]int)
			enumsInCode["SkillAttrOverlyingType"]["AddOverlying"] = 1
			enumsInCode["SkillAttrOverlyingType"]["MulOverlying"] = 2
			enumsInCode["SkillAttrOverlyingType"]["EnumOverlying"] = 3
			enumsInCode["SkillAttrOverlyingType"]["NoOverlying"] = 4

			enumsInCode["SkillDmgType"] = make(map[string]int)
			enumsInCode["SkillDmgType"]["Electrical"] = 1
			enumsInCode["SkillDmgType"]["Wind"] = 2
			enumsInCode["SkillDmgType"]["Light"] = 3
			enumsInCode["SkillDmgType"]["Fire"] = 4
			enumsInCode["SkillDmgType"]["Ice"] = 5
			enumsInCode["SkillDmgType"]["Physical"] = 6

			enumsInCode["SkillType"] = make(map[string]int)
			enumsInCode["SkillType"]["ActiveSkill"] = 1
			enumsInCode["SkillType"]["PassiveSkill"] = 2
			enumsInCode["SkillType"]["AuraSkill"] = 3

			enumsInCode["TaskCounterType"] = make(map[string]int)
			enumsInCode["TaskCounterType"]["Reset"] = 1
			enumsInCode["TaskCounterType"]["Total"] = 2

			enumsInCode["TaskType"] = make(map[string]int)
			enumsInCode["TaskType"]["Login"] = 1
			enumsInCode["TaskType"]["TotalLogin"] = 2
			enumsInCode["TaskType"]["LevelBegin"] = 3
			enumsInCode["TaskType"]["LevelPass"] = 4
			enumsInCode["TaskType"]["LevelPassTo"] = 5
			enumsInCode["TaskType"]["ItemBurn"] = 6
			enumsInCode["TaskType"]["TotalItemBurn"] = 7
			enumsInCode["TaskType"]["HeroLevelUp"] = 8
			enumsInCode["TaskType"]["HeroLevelUpTo"] = 9
			enumsInCode["TaskType"]["HeroStarUp"] = 10
			enumsInCode["TaskType"]["HeroStarUpTo"] = 11
			enumsInCode["TaskType"]["HeroSkillUp"] = 12
			enumsInCode["TaskType"]["HeroSkillUpTo"] = 13
			enumsInCode["TaskType"]["HeroGeneUp"] = 14
			enumsInCode["TaskType"]["HeroGeneUpTo"] = 15
			enumsInCode["TaskType"]["KillMonster"] = 16
			enumsInCode["TaskType"]["TotalKillMonster"] = 17
			enumsInCode["TaskType"]["claim_idle_reward"] = 18
			enumsInCode["TaskType"]["claim_pass_level_reward"] = 19
			enumsInCode["TaskType"]["Chat"] = 20
			enumsInCode["TaskType"]["Nigger"] = 21
			enumsInCode["TaskType"]["Rename"] = 22
			enumsInCode["TaskType"]["Avatar"] = 23
			enumsInCode["TaskType"]["JoinGuild"] = 24
			enumsInCode["TaskType"]["Sweep"] = 25
			enumsInCode["TaskType"]["HeroSummon"] = 26
			enumsInCode["TaskType"]["HeroConfig"] = 27
			enumsInCode["TaskType"]["LordEquipLvlUp"] = 28
			enumsInCode["TaskType"]["LordEquipLvlUpTo"] = 29
			enumsInCode["TaskType"]["GemCraft"] = 30
			enumsInCode["TaskType"]["GemSummon"] = 31
			enumsInCode["TaskType"]["Shopping"] = 32
			enumsInCode["TaskType"]["DungeonChallenge"] = 33
			enumsInCode["TaskType"]["DungeonSweep"] = 34
			enumsInCode["TaskType"]["ArenaChallenge"] = 35
			enumsInCode["TaskType"]["TotalActivateHero"] = 36

			enumsInCode["TriggerPackType"] = make(map[string]int)
			enumsInCode["TriggerPackType"]["LevelPass"] = 1
			enumsInCode["TriggerPackType"]["GemDraw"] = 2
			enumsInCode["TriggerPackType"]["HeroSummon"] = 3

		}
		enumsInJson := make(map[string]map[string]int)
		{
			jsonPath := filepath.Join(dir, "enum.json")
			bytes, err := os.ReadFile(jsonPath)
			if err != nil {
				return fmt.Errorf("[compatibility]please check json data, no enum.json, path=%s, err=[%w]", jsonPath, err)
			}
			data := &struct {
				Enums []*struct {
					EnumName string
					Cases    []*struct {
						CaseName  string
						CaseValue int32
					}
				}
			}{}
			if err = json.Unmarshal(bytes, data); err != nil {
				return fmt.Errorf("[compatibility]please check json data, unmarshal enum.json failed, path=%s, err=[%w]", jsonPath, err)
			}
			for _, enum := range data.Enums {
				enumsInJson[enum.EnumName] = make(map[string]int)
				for _, enumCase := range enum.Cases {
					enumsInJson[enum.EnumName][enumCase.CaseName] = int(enumCase.CaseValue)
				}
			}
		}
		for enumName, enumCasesInCode := range enumsInCode {
			enumCasesInJson, ok := enumsInJson[enumName]
			if !ok {
				return fmt.Errorf("[compatibility]please check json data, no enum %s in enum.json", enumName)
			}
			if len(enumCasesInCode) != len(enumCasesInJson) {
				return fmt.Errorf("[compatibility]please check json data, enum %s length not equal", enumName)
			}
			for caseName, caseValueInCode := range enumCasesInCode {
				caseValueInJson, ok := enumCasesInJson[caseName]
				if !ok {
					return fmt.Errorf("[compatibility]please check json data, no enum %s case %s in enum.json", enumName, caseName)
				}
				if caseValueInCode != caseValueInJson {
					return fmt.Errorf("[compatibility]please check json data, enum %s case %s value not equal", enumName, caseName)
				}
			}
		}
	}
	return nil
}

type LoadJsonOptions struct {
	EnableCheckCompatibility bool
}

type LoadJsonOption func(*LoadJsonOptions)

func EnableCheckCompatibility() LoadJsonOption {
	return func(options *LoadJsonOptions) {
		options.EnableCheckCompatibility = true
	}
}

func (c *Configs) LoadJson(dir string, options ...LoadJsonOption) error {
	var opts = &LoadJsonOptions{}
	for _, option := range options {
		option(opts)
	}
	var err error
	if err = c.checkJsonVersion(dir, opts.EnableCheckCompatibility); err != nil {
		return err
	}

	if err = c.ActivityTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaBotTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaShopTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.AvatarTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.BattleModelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.BenefitsTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DailyTasksTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DaveLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DropGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DropMainTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonRefreshMonsterEventTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.FunctionTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GameConfigs.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GoToTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildFlagTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildRankTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildShopTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildTaskTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroBondsTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroCareerTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroConfigTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroGeneTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroQualityTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroStarTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.HeroTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.Iap1stTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.Iap2XTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapBPTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapDealTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapPackageTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapPriceTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapShopMallTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapSignTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IdleRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.IdleRewardTime.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ItemQualityTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ItemSourceTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ItemTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LanguageCnTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LoginOpenTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordEquipTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.LordGemTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MailTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainChapterTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventPropTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapEventTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ModifierTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.NewbieTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.PresetsTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RankMainTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RankRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeTabTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.SelectChestMainTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.ShopTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.Sign7Table.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TowerAITable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TowerTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TurnRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.TurnTable.loadJson(dir, c); err != nil {
		return err
	}
	if err = c.VehicleTable.loadJson(dir, c); err != nil {
		return err
	}

	if err = c.ActivityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaBotTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaChallengeRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaDailyRankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaExtraChallengeCntTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaMatchTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaRefreshTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ArenaWeeklyRankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AttributeHierarchyTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AvatarFrameTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.AvatarTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BattleAttributeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BattleModelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsCalcJustShowTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsCalcTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.BenefitsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DailyTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DailyTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DaveLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DropGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DropMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonChapterLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonCoinLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonGeneLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonLordEquipLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonRefreshMonsterEventTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonSunshineLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.DungeonTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.FunctionPreviewTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.FunctionTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GameConfigs.setupIndexes(); err != nil {
		return err
	}
	if err = c.GemAffixQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GemQualityTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GoToTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildFlagTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildHaggleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildPermissionTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildRankTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildTaskTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.GuildTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroBondsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroCareerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroConfigTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroFragmentTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroGeneFragmentTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroGeneTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryMustTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroLotteryRandomTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroRestrainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillAttrTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillAwakeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillBuffTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillEffectTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroSkillTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroStarTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.HeroTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Iap1stTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Iap2XTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapAdFreeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapBPTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapBpRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleFreeRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDailySaleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapDealTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapLevelFundRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapLevelFundTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapMonthCardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageDiamondShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPackageTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapPriceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapRegularPackGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapRegularPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapShopMallTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapSignRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapSignTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTriggerPackGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTriggerPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IapTurnPackTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleMonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.IdleRewardTime.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemSourceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ItemTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LanguageCnTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LoginOpenTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipGradeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipGradeTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordEquipTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemCraftTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemDropCntTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemDropQualityTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupChanceTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupMustTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemRandomRewardGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemReforgeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.LordGemTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MailTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainChapterLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainChapterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelPassRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRangeDmgTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRewardRatioTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelRogueRewardWeightTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MainLineTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventBuffTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventMonsterGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventMonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventObstacleTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventPropTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventSkillTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapEventTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MapRefreshMonsterEventTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ModifierTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterCareerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterGradeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterPosTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterSkillTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.MonsterTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.NewbieTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.NpcDialogueTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.PhotovoltaicTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.PresetsTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RankMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RankRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeRefreshTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabEffectTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabGroupRandomTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabNewbieTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeTabTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.RougeWeightCoef.setupIndexes(); err != nil {
		return err
	}
	if err = c.SelectChestGroupTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.SelectChestMainTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.SevenDayTasksScoreTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.SevenDayTasksTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.ShopTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.Sign7Table.setupIndexes(); err != nil {
		return err
	}
	if err = c.SkillDmgTypeTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerAILevelTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerAITable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TowerTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnScoreRewardTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.TurnTable.setupIndexes(); err != nil {
		return err
	}
	if err = c.VehicleTable.setupIndexes(); err != nil {
		return err
	}

	c.ActivityTable.bindRefs(c)
	c.ArenaBotTable.bindRefs(c)
	c.ArenaChallengeRewardTable.bindRefs(c)
	c.ArenaDailyRankRewardTable.bindRefs(c)
	c.ArenaExtraChallengeCntTable.bindRefs(c)
	c.ArenaMatchTable.bindRefs(c)
	c.ArenaRefreshTable.bindRefs(c)
	c.ArenaScoreTable.bindRefs(c)
	c.ArenaShopTable.bindRefs(c)
	c.ArenaWeeklyRankRewardTable.bindRefs(c)
	c.AttributeHierarchyTable.bindRefs(c)
	c.AvatarFrameTable.bindRefs(c)
	c.AvatarTable.bindRefs(c)
	c.BattleAttributeTable.bindRefs(c)
	c.BattleModelTable.bindRefs(c)
	c.BenefitsCalcJustShowTable.bindRefs(c)
	c.BenefitsCalcTable.bindRefs(c)
	c.BenefitsTable.bindRefs(c)
	c.DailyTasksScoreTable.bindRefs(c)
	c.DailyTasksTable.bindRefs(c)
	c.DaveLevelTable.bindRefs(c)
	c.DropGroupTable.bindRefs(c)
	c.DropMainTable.bindRefs(c)
	c.DungeonChapterLevelTable.bindRefs(c)
	c.DungeonCoinLevelTable.bindRefs(c)
	c.DungeonGeneLevelTable.bindRefs(c)
	c.DungeonLordEquipLevelTable.bindRefs(c)
	c.DungeonRefreshMonsterEventTable.bindRefs(c)
	c.DungeonSunshineLevelTable.bindRefs(c)
	c.DungeonTypeTable.bindRefs(c)
	c.FunctionPreviewTable.bindRefs(c)
	c.FunctionTable.bindRefs(c)
	c.GameConfigs.bindRefs(c)
	c.GemAffixQualityTable.bindRefs(c)
	c.GemQualityTypeTable.bindRefs(c)
	c.GoToTable.bindRefs(c)
	c.GuildFlagTable.bindRefs(c)
	c.GuildHaggleTable.bindRefs(c)
	c.GuildLevelTable.bindRefs(c)
	c.GuildPermissionTable.bindRefs(c)
	c.GuildRankTable.bindRefs(c)
	c.GuildShopTable.bindRefs(c)
	c.GuildTaskTable.bindRefs(c)
	c.GuildTasksScoreTable.bindRefs(c)
	c.HeroBondsTable.bindRefs(c)
	c.HeroCareerTable.bindRefs(c)
	c.HeroConfigTable.bindRefs(c)
	c.HeroFragmentTable.bindRefs(c)
	c.HeroGeneFragmentTable.bindRefs(c)
	c.HeroGeneTable.bindRefs(c)
	c.HeroLevelTable.bindRefs(c)
	c.HeroLotteryGroupTable.bindRefs(c)
	c.HeroLotteryMustTable.bindRefs(c)
	c.HeroLotteryRandomGroupTable.bindRefs(c)
	c.HeroLotteryRandomTable.bindRefs(c)
	c.HeroQualityTable.bindRefs(c)
	c.HeroRestrainTable.bindRefs(c)
	c.HeroSkillAttrTable.bindRefs(c)
	c.HeroSkillAwakeTable.bindRefs(c)
	c.HeroSkillBuffTable.bindRefs(c)
	c.HeroSkillBuffTypeTable.bindRefs(c)
	c.HeroSkillEffectTable.bindRefs(c)
	c.HeroSkillGroupTable.bindRefs(c)
	c.HeroSkillTypeTable.bindRefs(c)
	c.HeroStarTable.bindRefs(c)
	c.HeroTable.bindRefs(c)
	c.HeroTypeTable.bindRefs(c)
	c.Iap1stTable.bindRefs(c)
	c.Iap2XTable.bindRefs(c)
	c.IapAdFreeTable.bindRefs(c)
	c.IapBPTable.bindRefs(c)
	c.IapBpRewardTable.bindRefs(c)
	c.IapDailySaleFreeRewardTable.bindRefs(c)
	c.IapDailySaleRewardGroupTable.bindRefs(c)
	c.IapDailySaleRewardTable.bindRefs(c)
	c.IapDailySaleTable.bindRefs(c)
	c.IapDealTable.bindRefs(c)
	c.IapLevelFundRewardTable.bindRefs(c)
	c.IapLevelFundTable.bindRefs(c)
	c.IapMonthCardTable.bindRefs(c)
	c.IapPackageDiamondShopTable.bindRefs(c)
	c.IapPackageRewardTable.bindRefs(c)
	c.IapPackageTable.bindRefs(c)
	c.IapPriceTable.bindRefs(c)
	c.IapRegularPackGroupTable.bindRefs(c)
	c.IapRegularPackTable.bindRefs(c)
	c.IapShopMallTable.bindRefs(c)
	c.IapSignRewardTable.bindRefs(c)
	c.IapSignTable.bindRefs(c)
	c.IapTriggerPackGroupTable.bindRefs(c)
	c.IapTriggerPackTable.bindRefs(c)
	c.IapTurnPackTable.bindRefs(c)
	c.IdleMonsterTable.bindRefs(c)
	c.IdleRewardTable.bindRefs(c)
	c.IdleRewardTime.bindRefs(c)
	c.ItemQualityTable.bindRefs(c)
	c.ItemSourceTable.bindRefs(c)
	c.ItemTable.bindRefs(c)
	c.LanguageCnTable.bindRefs(c)
	c.LoginOpenTable.bindRefs(c)
	c.LordEquipGradeTable.bindRefs(c)
	c.LordEquipGradeTypeTable.bindRefs(c)
	c.LordEquipTable.bindRefs(c)
	c.LordEquipTypeTable.bindRefs(c)
	c.LordGemCraftTable.bindRefs(c)
	c.LordGemDropCntTable.bindRefs(c)
	c.LordGemDropQualityTable.bindRefs(c)
	c.LordGemRandomGroupChanceTable.bindRefs(c)
	c.LordGemRandomGroupMustTable.bindRefs(c)
	c.LordGemRandomGroupTable.bindRefs(c)
	c.LordGemRandomRewardGroupTable.bindRefs(c)
	c.LordGemReforgeTable.bindRefs(c)
	c.LordGemTable.bindRefs(c)
	c.MailTable.bindRefs(c)
	c.MainChapterLevelTable.bindRefs(c)
	c.MainChapterTable.bindRefs(c)
	c.MainLevelPassRewardTable.bindRefs(c)
	c.MainLevelRangeDmgTable.bindRefs(c)
	c.MainLevelRewardRatioTable.bindRefs(c)
	c.MainLevelRewardTable.bindRefs(c)
	c.MainLevelRogueRewardWeightTable.bindRefs(c)
	c.MainLevelTable.bindRefs(c)
	c.MainLineTasksTable.bindRefs(c)
	c.MapEventBuffTable.bindRefs(c)
	c.MapEventMonsterGroupTable.bindRefs(c)
	c.MapEventMonsterTable.bindRefs(c)
	c.MapEventObstacleTable.bindRefs(c)
	c.MapEventPropTable.bindRefs(c)
	c.MapEventRewardTable.bindRefs(c)
	c.MapEventSkillTable.bindRefs(c)
	c.MapEventTable.bindRefs(c)
	c.MapRefreshMonsterEventTable.bindRefs(c)
	c.ModifierTable.bindRefs(c)
	c.MonsterCareerTable.bindRefs(c)
	c.MonsterGradeTable.bindRefs(c)
	c.MonsterPosTypeTable.bindRefs(c)
	c.MonsterSkillTable.bindRefs(c)
	c.MonsterTable.bindRefs(c)
	c.MonsterTypeTable.bindRefs(c)
	c.NewbieTable.bindRefs(c)
	c.NpcDialogueTable.bindRefs(c)
	c.PhotovoltaicTable.bindRefs(c)
	c.PresetsTable.bindRefs(c)
	c.RankMainTable.bindRefs(c)
	c.RankRewardTable.bindRefs(c)
	c.RougeRefreshTable.bindRefs(c)
	c.RougeTabEffectTable.bindRefs(c)
	c.RougeTabGroupRandomTable.bindRefs(c)
	c.RougeTabGroupTable.bindRefs(c)
	c.RougeTabNewbieTable.bindRefs(c)
	c.RougeTabTable.bindRefs(c)
	c.RougeWeightCoef.bindRefs(c)
	c.SelectChestGroupTable.bindRefs(c)
	c.SelectChestMainTable.bindRefs(c)
	c.SevenDayTasksScoreTable.bindRefs(c)
	c.SevenDayTasksTable.bindRefs(c)
	c.ShopTable.bindRefs(c)
	c.Sign7Table.bindRefs(c)
	c.SkillDmgTypeTable.bindRefs(c)
	c.TowerAILevelTable.bindRefs(c)
	c.TowerAITable.bindRefs(c)
	c.TowerTable.bindRefs(c)
	c.TurnRewardTable.bindRefs(c)
	c.TurnScoreRewardTable.bindRefs(c)
	c.TurnTable.bindRefs(c)
	c.VehicleTable.bindRefs(c)

	return nil
}

// @title LoadJsonPartially
// @description 部分加载json数据
// @param dir string json文件目录
// @param tableNames []string 需要加载的表名，目前支持了大写驼峰格式，即与程序中表名一致
// @param options ...LoadJsonOption options
func (c *Configs) LoadJsonPartially(dir string, tableNames []string, options ...LoadJsonOption) error {
	var opts = &LoadJsonOptions{}
	for _, option := range options {
		option(opts)
	}
	var err error
	if err = c.checkJsonVersion(dir, opts.EnableCheckCompatibility); err != nil {
		return err
	}

	if len(tableNames) <= 0 {
		return nil
	}
	m := make(map[string]struct{})
	for _, tableName := range tableNames {
		trimmed := strings.TrimSuffix(strings.TrimSpace(tableName), ".json")
		if len(trimmed) <= 0 {
			continue
		}
		m[trimmed] = struct{}{}
	}
	if len(m) <= 0 {
		return nil
	}
	for tableName := range m {
		if tableName == "ActivityTable" {
			if err = c.ActivityTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaBotTable" {
			if err = c.ArenaBotTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaChallengeRewardTable" {
			if err = c.ArenaChallengeRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaDailyRankRewardTable" {
			if err = c.ArenaDailyRankRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaExtraChallengeCntTable" {
			if err = c.ArenaExtraChallengeCntTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaMatchTable" {
			if err = c.ArenaMatchTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaRefreshTable" {
			if err = c.ArenaRefreshTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaScoreTable" {
			if err = c.ArenaScoreTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaShopTable" {
			if err = c.ArenaShopTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ArenaWeeklyRankRewardTable" {
			if err = c.ArenaWeeklyRankRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "AttributeHierarchyTable" {
			if err = c.AttributeHierarchyTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "AvatarFrameTable" {
			if err = c.AvatarFrameTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "AvatarTable" {
			if err = c.AvatarTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "BattleAttributeTable" {
			if err = c.BattleAttributeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "BattleModelTable" {
			if err = c.BattleModelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "BenefitsCalcJustShowTable" {
			if err = c.BenefitsCalcJustShowTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "BenefitsCalcTable" {
			if err = c.BenefitsCalcTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "BenefitsTable" {
			if err = c.BenefitsTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DailyTasksScoreTable" {
			if err = c.DailyTasksScoreTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DailyTasksTable" {
			if err = c.DailyTasksTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DaveLevelTable" {
			if err = c.DaveLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DropGroupTable" {
			if err = c.DropGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DropMainTable" {
			if err = c.DropMainTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonChapterLevelTable" {
			if err = c.DungeonChapterLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonCoinLevelTable" {
			if err = c.DungeonCoinLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonGeneLevelTable" {
			if err = c.DungeonGeneLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonLordEquipLevelTable" {
			if err = c.DungeonLordEquipLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonRefreshMonsterEventTable" {
			if err = c.DungeonRefreshMonsterEventTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonSunshineLevelTable" {
			if err = c.DungeonSunshineLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "DungeonTypeTable" {
			if err = c.DungeonTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "FunctionPreviewTable" {
			if err = c.FunctionPreviewTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "FunctionTable" {
			if err = c.FunctionTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GameConfigs" {
			if err = c.GameConfigs.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GemAffixQualityTable" {
			if err = c.GemAffixQualityTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GemQualityTypeTable" {
			if err = c.GemQualityTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GoToTable" {
			if err = c.GoToTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildFlagTable" {
			if err = c.GuildFlagTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildHaggleTable" {
			if err = c.GuildHaggleTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildLevelTable" {
			if err = c.GuildLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildPermissionTable" {
			if err = c.GuildPermissionTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildRankTable" {
			if err = c.GuildRankTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildShopTable" {
			if err = c.GuildShopTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildTaskTable" {
			if err = c.GuildTaskTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "GuildTasksScoreTable" {
			if err = c.GuildTasksScoreTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroBondsTable" {
			if err = c.HeroBondsTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroCareerTable" {
			if err = c.HeroCareerTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroConfigTable" {
			if err = c.HeroConfigTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroFragmentTable" {
			if err = c.HeroFragmentTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroGeneFragmentTable" {
			if err = c.HeroGeneFragmentTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroGeneTable" {
			if err = c.HeroGeneTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroLevelTable" {
			if err = c.HeroLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryGroupTable" {
			if err = c.HeroLotteryGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryMustTable" {
			if err = c.HeroLotteryMustTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryRandomGroupTable" {
			if err = c.HeroLotteryRandomGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryRandomTable" {
			if err = c.HeroLotteryRandomTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroQualityTable" {
			if err = c.HeroQualityTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroRestrainTable" {
			if err = c.HeroRestrainTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillAttrTable" {
			if err = c.HeroSkillAttrTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillAwakeTable" {
			if err = c.HeroSkillAwakeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillBuffTable" {
			if err = c.HeroSkillBuffTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillBuffTypeTable" {
			if err = c.HeroSkillBuffTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillEffectTable" {
			if err = c.HeroSkillEffectTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillGroupTable" {
			if err = c.HeroSkillGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillTypeTable" {
			if err = c.HeroSkillTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroStarTable" {
			if err = c.HeroStarTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroTable" {
			if err = c.HeroTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "HeroTypeTable" {
			if err = c.HeroTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "Iap1stTable" {
			if err = c.Iap1stTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "Iap2XTable" {
			if err = c.Iap2XTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapAdFreeTable" {
			if err = c.IapAdFreeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapBPTable" {
			if err = c.IapBPTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapBpRewardTable" {
			if err = c.IapBpRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleFreeRewardTable" {
			if err = c.IapDailySaleFreeRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleRewardGroupTable" {
			if err = c.IapDailySaleRewardGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleRewardTable" {
			if err = c.IapDailySaleRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleTable" {
			if err = c.IapDailySaleTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapDealTable" {
			if err = c.IapDealTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapLevelFundRewardTable" {
			if err = c.IapLevelFundRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapLevelFundTable" {
			if err = c.IapLevelFundTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapMonthCardTable" {
			if err = c.IapMonthCardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapPackageDiamondShopTable" {
			if err = c.IapPackageDiamondShopTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapPackageRewardTable" {
			if err = c.IapPackageRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapPackageTable" {
			if err = c.IapPackageTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapPriceTable" {
			if err = c.IapPriceTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapRegularPackGroupTable" {
			if err = c.IapRegularPackGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapRegularPackTable" {
			if err = c.IapRegularPackTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapShopMallTable" {
			if err = c.IapShopMallTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapSignRewardTable" {
			if err = c.IapSignRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapSignTable" {
			if err = c.IapSignTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapTriggerPackGroupTable" {
			if err = c.IapTriggerPackGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapTriggerPackTable" {
			if err = c.IapTriggerPackTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IapTurnPackTable" {
			if err = c.IapTurnPackTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IdleMonsterTable" {
			if err = c.IdleMonsterTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IdleRewardTable" {
			if err = c.IdleRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "IdleRewardTime" {
			if err = c.IdleRewardTime.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ItemQualityTable" {
			if err = c.ItemQualityTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ItemSourceTable" {
			if err = c.ItemSourceTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ItemTable" {
			if err = c.ItemTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LanguageCnTable" {
			if err = c.LanguageCnTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LoginOpenTable" {
			if err = c.LoginOpenTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordEquipGradeTable" {
			if err = c.LordEquipGradeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordEquipGradeTypeTable" {
			if err = c.LordEquipGradeTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordEquipTable" {
			if err = c.LordEquipTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordEquipTypeTable" {
			if err = c.LordEquipTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemCraftTable" {
			if err = c.LordGemCraftTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemDropCntTable" {
			if err = c.LordGemDropCntTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemDropQualityTable" {
			if err = c.LordGemDropQualityTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupChanceTable" {
			if err = c.LordGemRandomGroupChanceTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupMustTable" {
			if err = c.LordGemRandomGroupMustTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupTable" {
			if err = c.LordGemRandomGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomRewardGroupTable" {
			if err = c.LordGemRandomRewardGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemReforgeTable" {
			if err = c.LordGemReforgeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "LordGemTable" {
			if err = c.LordGemTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MailTable" {
			if err = c.MailTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainChapterLevelTable" {
			if err = c.MainChapterLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainChapterTable" {
			if err = c.MainChapterTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelPassRewardTable" {
			if err = c.MainLevelPassRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRangeDmgTable" {
			if err = c.MainLevelRangeDmgTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRewardRatioTable" {
			if err = c.MainLevelRewardRatioTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRewardTable" {
			if err = c.MainLevelRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRogueRewardWeightTable" {
			if err = c.MainLevelRogueRewardWeightTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLevelTable" {
			if err = c.MainLevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MainLineTasksTable" {
			if err = c.MainLineTasksTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventBuffTable" {
			if err = c.MapEventBuffTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventMonsterGroupTable" {
			if err = c.MapEventMonsterGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventMonsterTable" {
			if err = c.MapEventMonsterTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventObstacleTable" {
			if err = c.MapEventObstacleTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventPropTable" {
			if err = c.MapEventPropTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventRewardTable" {
			if err = c.MapEventRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventSkillTable" {
			if err = c.MapEventSkillTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapEventTable" {
			if err = c.MapEventTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MapRefreshMonsterEventTable" {
			if err = c.MapRefreshMonsterEventTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ModifierTable" {
			if err = c.ModifierTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterCareerTable" {
			if err = c.MonsterCareerTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterGradeTable" {
			if err = c.MonsterGradeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterPosTypeTable" {
			if err = c.MonsterPosTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterSkillTable" {
			if err = c.MonsterSkillTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterTable" {
			if err = c.MonsterTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "MonsterTypeTable" {
			if err = c.MonsterTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "NewbieTable" {
			if err = c.NewbieTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "NpcDialogueTable" {
			if err = c.NpcDialogueTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "PhotovoltaicTable" {
			if err = c.PhotovoltaicTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "PresetsTable" {
			if err = c.PresetsTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RankMainTable" {
			if err = c.RankMainTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RankRewardTable" {
			if err = c.RankRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeRefreshTable" {
			if err = c.RougeRefreshTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeTabEffectTable" {
			if err = c.RougeTabEffectTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeTabGroupRandomTable" {
			if err = c.RougeTabGroupRandomTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeTabGroupTable" {
			if err = c.RougeTabGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeTabNewbieTable" {
			if err = c.RougeTabNewbieTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeTabTable" {
			if err = c.RougeTabTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "RougeWeightCoef" {
			if err = c.RougeWeightCoef.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "SelectChestGroupTable" {
			if err = c.SelectChestGroupTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "SelectChestMainTable" {
			if err = c.SelectChestMainTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "SevenDayTasksScoreTable" {
			if err = c.SevenDayTasksScoreTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "SevenDayTasksTable" {
			if err = c.SevenDayTasksTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "ShopTable" {
			if err = c.ShopTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "Sign7Table" {
			if err = c.Sign7Table.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "SkillDmgTypeTable" {
			if err = c.SkillDmgTypeTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TowerAILevelTable" {
			if err = c.TowerAILevelTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TowerAITable" {
			if err = c.TowerAITable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TowerTable" {
			if err = c.TowerTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TurnRewardTable" {
			if err = c.TurnRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TurnScoreRewardTable" {
			if err = c.TurnScoreRewardTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "TurnTable" {
			if err = c.TurnTable.loadJson(dir, c); err != nil {
				return err
			}
		}
		if tableName == "VehicleTable" {
			if err = c.VehicleTable.loadJson(dir, c); err != nil {
				return err
			}
		}

	}
	for tableName := range m {
		if tableName == "ActivityTable" {
			if err = c.ActivityTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaBotTable" {
			if err = c.ArenaBotTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaChallengeRewardTable" {
			if err = c.ArenaChallengeRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaDailyRankRewardTable" {
			if err = c.ArenaDailyRankRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaExtraChallengeCntTable" {
			if err = c.ArenaExtraChallengeCntTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaMatchTable" {
			if err = c.ArenaMatchTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaRefreshTable" {
			if err = c.ArenaRefreshTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaScoreTable" {
			if err = c.ArenaScoreTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaShopTable" {
			if err = c.ArenaShopTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ArenaWeeklyRankRewardTable" {
			if err = c.ArenaWeeklyRankRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "AttributeHierarchyTable" {
			if err = c.AttributeHierarchyTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "AvatarFrameTable" {
			if err = c.AvatarFrameTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "AvatarTable" {
			if err = c.AvatarTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "BattleAttributeTable" {
			if err = c.BattleAttributeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "BattleModelTable" {
			if err = c.BattleModelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "BenefitsCalcJustShowTable" {
			if err = c.BenefitsCalcJustShowTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "BenefitsCalcTable" {
			if err = c.BenefitsCalcTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "BenefitsTable" {
			if err = c.BenefitsTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DailyTasksScoreTable" {
			if err = c.DailyTasksScoreTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DailyTasksTable" {
			if err = c.DailyTasksTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DaveLevelTable" {
			if err = c.DaveLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DropGroupTable" {
			if err = c.DropGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DropMainTable" {
			if err = c.DropMainTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonChapterLevelTable" {
			if err = c.DungeonChapterLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonCoinLevelTable" {
			if err = c.DungeonCoinLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonGeneLevelTable" {
			if err = c.DungeonGeneLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonLordEquipLevelTable" {
			if err = c.DungeonLordEquipLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonRefreshMonsterEventTable" {
			if err = c.DungeonRefreshMonsterEventTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonSunshineLevelTable" {
			if err = c.DungeonSunshineLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "DungeonTypeTable" {
			if err = c.DungeonTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "FunctionPreviewTable" {
			if err = c.FunctionPreviewTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "FunctionTable" {
			if err = c.FunctionTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GameConfigs" {
			if err = c.GameConfigs.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GemAffixQualityTable" {
			if err = c.GemAffixQualityTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GemQualityTypeTable" {
			if err = c.GemQualityTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GoToTable" {
			if err = c.GoToTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildFlagTable" {
			if err = c.GuildFlagTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildHaggleTable" {
			if err = c.GuildHaggleTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildLevelTable" {
			if err = c.GuildLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildPermissionTable" {
			if err = c.GuildPermissionTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildRankTable" {
			if err = c.GuildRankTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildShopTable" {
			if err = c.GuildShopTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildTaskTable" {
			if err = c.GuildTaskTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "GuildTasksScoreTable" {
			if err = c.GuildTasksScoreTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroBondsTable" {
			if err = c.HeroBondsTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroCareerTable" {
			if err = c.HeroCareerTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroConfigTable" {
			if err = c.HeroConfigTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroFragmentTable" {
			if err = c.HeroFragmentTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroGeneFragmentTable" {
			if err = c.HeroGeneFragmentTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroGeneTable" {
			if err = c.HeroGeneTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroLevelTable" {
			if err = c.HeroLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryGroupTable" {
			if err = c.HeroLotteryGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryMustTable" {
			if err = c.HeroLotteryMustTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryRandomGroupTable" {
			if err = c.HeroLotteryRandomGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroLotteryRandomTable" {
			if err = c.HeroLotteryRandomTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroQualityTable" {
			if err = c.HeroQualityTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroRestrainTable" {
			if err = c.HeroRestrainTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillAttrTable" {
			if err = c.HeroSkillAttrTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillAwakeTable" {
			if err = c.HeroSkillAwakeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillBuffTable" {
			if err = c.HeroSkillBuffTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillBuffTypeTable" {
			if err = c.HeroSkillBuffTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillEffectTable" {
			if err = c.HeroSkillEffectTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillGroupTable" {
			if err = c.HeroSkillGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroSkillTypeTable" {
			if err = c.HeroSkillTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroStarTable" {
			if err = c.HeroStarTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroTable" {
			if err = c.HeroTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "HeroTypeTable" {
			if err = c.HeroTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "Iap1stTable" {
			if err = c.Iap1stTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "Iap2XTable" {
			if err = c.Iap2XTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapAdFreeTable" {
			if err = c.IapAdFreeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapBPTable" {
			if err = c.IapBPTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapBpRewardTable" {
			if err = c.IapBpRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleFreeRewardTable" {
			if err = c.IapDailySaleFreeRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleRewardGroupTable" {
			if err = c.IapDailySaleRewardGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleRewardTable" {
			if err = c.IapDailySaleRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapDailySaleTable" {
			if err = c.IapDailySaleTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapDealTable" {
			if err = c.IapDealTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapLevelFundRewardTable" {
			if err = c.IapLevelFundRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapLevelFundTable" {
			if err = c.IapLevelFundTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapMonthCardTable" {
			if err = c.IapMonthCardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapPackageDiamondShopTable" {
			if err = c.IapPackageDiamondShopTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapPackageRewardTable" {
			if err = c.IapPackageRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapPackageTable" {
			if err = c.IapPackageTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapPriceTable" {
			if err = c.IapPriceTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapRegularPackGroupTable" {
			if err = c.IapRegularPackGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapRegularPackTable" {
			if err = c.IapRegularPackTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapShopMallTable" {
			if err = c.IapShopMallTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapSignRewardTable" {
			if err = c.IapSignRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapSignTable" {
			if err = c.IapSignTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapTriggerPackGroupTable" {
			if err = c.IapTriggerPackGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapTriggerPackTable" {
			if err = c.IapTriggerPackTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IapTurnPackTable" {
			if err = c.IapTurnPackTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IdleMonsterTable" {
			if err = c.IdleMonsterTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IdleRewardTable" {
			if err = c.IdleRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "IdleRewardTime" {
			if err = c.IdleRewardTime.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ItemQualityTable" {
			if err = c.ItemQualityTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ItemSourceTable" {
			if err = c.ItemSourceTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ItemTable" {
			if err = c.ItemTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LanguageCnTable" {
			if err = c.LanguageCnTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LoginOpenTable" {
			if err = c.LoginOpenTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordEquipGradeTable" {
			if err = c.LordEquipGradeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordEquipGradeTypeTable" {
			if err = c.LordEquipGradeTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordEquipTable" {
			if err = c.LordEquipTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordEquipTypeTable" {
			if err = c.LordEquipTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemCraftTable" {
			if err = c.LordGemCraftTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemDropCntTable" {
			if err = c.LordGemDropCntTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemDropQualityTable" {
			if err = c.LordGemDropQualityTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupChanceTable" {
			if err = c.LordGemRandomGroupChanceTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupMustTable" {
			if err = c.LordGemRandomGroupMustTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomGroupTable" {
			if err = c.LordGemRandomGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemRandomRewardGroupTable" {
			if err = c.LordGemRandomRewardGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemReforgeTable" {
			if err = c.LordGemReforgeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "LordGemTable" {
			if err = c.LordGemTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MailTable" {
			if err = c.MailTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainChapterLevelTable" {
			if err = c.MainChapterLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainChapterTable" {
			if err = c.MainChapterTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelPassRewardTable" {
			if err = c.MainLevelPassRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRangeDmgTable" {
			if err = c.MainLevelRangeDmgTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRewardRatioTable" {
			if err = c.MainLevelRewardRatioTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRewardTable" {
			if err = c.MainLevelRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelRogueRewardWeightTable" {
			if err = c.MainLevelRogueRewardWeightTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLevelTable" {
			if err = c.MainLevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MainLineTasksTable" {
			if err = c.MainLineTasksTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventBuffTable" {
			if err = c.MapEventBuffTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventMonsterGroupTable" {
			if err = c.MapEventMonsterGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventMonsterTable" {
			if err = c.MapEventMonsterTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventObstacleTable" {
			if err = c.MapEventObstacleTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventPropTable" {
			if err = c.MapEventPropTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventRewardTable" {
			if err = c.MapEventRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventSkillTable" {
			if err = c.MapEventSkillTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapEventTable" {
			if err = c.MapEventTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MapRefreshMonsterEventTable" {
			if err = c.MapRefreshMonsterEventTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ModifierTable" {
			if err = c.ModifierTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterCareerTable" {
			if err = c.MonsterCareerTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterGradeTable" {
			if err = c.MonsterGradeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterPosTypeTable" {
			if err = c.MonsterPosTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterSkillTable" {
			if err = c.MonsterSkillTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterTable" {
			if err = c.MonsterTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "MonsterTypeTable" {
			if err = c.MonsterTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "NewbieTable" {
			if err = c.NewbieTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "NpcDialogueTable" {
			if err = c.NpcDialogueTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "PhotovoltaicTable" {
			if err = c.PhotovoltaicTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "PresetsTable" {
			if err = c.PresetsTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RankMainTable" {
			if err = c.RankMainTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RankRewardTable" {
			if err = c.RankRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeRefreshTable" {
			if err = c.RougeRefreshTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeTabEffectTable" {
			if err = c.RougeTabEffectTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeTabGroupRandomTable" {
			if err = c.RougeTabGroupRandomTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeTabGroupTable" {
			if err = c.RougeTabGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeTabNewbieTable" {
			if err = c.RougeTabNewbieTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeTabTable" {
			if err = c.RougeTabTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "RougeWeightCoef" {
			if err = c.RougeWeightCoef.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "SelectChestGroupTable" {
			if err = c.SelectChestGroupTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "SelectChestMainTable" {
			if err = c.SelectChestMainTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "SevenDayTasksScoreTable" {
			if err = c.SevenDayTasksScoreTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "SevenDayTasksTable" {
			if err = c.SevenDayTasksTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "ShopTable" {
			if err = c.ShopTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "Sign7Table" {
			if err = c.Sign7Table.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "SkillDmgTypeTable" {
			if err = c.SkillDmgTypeTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TowerAILevelTable" {
			if err = c.TowerAILevelTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TowerAITable" {
			if err = c.TowerAITable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TowerTable" {
			if err = c.TowerTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TurnRewardTable" {
			if err = c.TurnRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TurnScoreRewardTable" {
			if err = c.TurnScoreRewardTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "TurnTable" {
			if err = c.TurnTable.setupIndexes(); err != nil {
				return err
			}
		}
		if tableName == "VehicleTable" {
			if err = c.VehicleTable.setupIndexes(); err != nil {
				return err
			}
		}

	}
	for tableName := range m {
		if tableName == "ActivityTable" {
			c.ActivityTable.bindRefs(c)
		}
		if tableName == "ArenaBotTable" {
			c.ArenaBotTable.bindRefs(c)
		}
		if tableName == "ArenaChallengeRewardTable" {
			c.ArenaChallengeRewardTable.bindRefs(c)
		}
		if tableName == "ArenaDailyRankRewardTable" {
			c.ArenaDailyRankRewardTable.bindRefs(c)
		}
		if tableName == "ArenaExtraChallengeCntTable" {
			c.ArenaExtraChallengeCntTable.bindRefs(c)
		}
		if tableName == "ArenaMatchTable" {
			c.ArenaMatchTable.bindRefs(c)
		}
		if tableName == "ArenaRefreshTable" {
			c.ArenaRefreshTable.bindRefs(c)
		}
		if tableName == "ArenaScoreTable" {
			c.ArenaScoreTable.bindRefs(c)
		}
		if tableName == "ArenaShopTable" {
			c.ArenaShopTable.bindRefs(c)
		}
		if tableName == "ArenaWeeklyRankRewardTable" {
			c.ArenaWeeklyRankRewardTable.bindRefs(c)
		}
		if tableName == "AttributeHierarchyTable" {
			c.AttributeHierarchyTable.bindRefs(c)
		}
		if tableName == "AvatarFrameTable" {
			c.AvatarFrameTable.bindRefs(c)
		}
		if tableName == "AvatarTable" {
			c.AvatarTable.bindRefs(c)
		}
		if tableName == "BattleAttributeTable" {
			c.BattleAttributeTable.bindRefs(c)
		}
		if tableName == "BattleModelTable" {
			c.BattleModelTable.bindRefs(c)
		}
		if tableName == "BenefitsCalcJustShowTable" {
			c.BenefitsCalcJustShowTable.bindRefs(c)
		}
		if tableName == "BenefitsCalcTable" {
			c.BenefitsCalcTable.bindRefs(c)
		}
		if tableName == "BenefitsTable" {
			c.BenefitsTable.bindRefs(c)
		}
		if tableName == "DailyTasksScoreTable" {
			c.DailyTasksScoreTable.bindRefs(c)
		}
		if tableName == "DailyTasksTable" {
			c.DailyTasksTable.bindRefs(c)
		}
		if tableName == "DaveLevelTable" {
			c.DaveLevelTable.bindRefs(c)
		}
		if tableName == "DropGroupTable" {
			c.DropGroupTable.bindRefs(c)
		}
		if tableName == "DropMainTable" {
			c.DropMainTable.bindRefs(c)
		}
		if tableName == "DungeonChapterLevelTable" {
			c.DungeonChapterLevelTable.bindRefs(c)
		}
		if tableName == "DungeonCoinLevelTable" {
			c.DungeonCoinLevelTable.bindRefs(c)
		}
		if tableName == "DungeonGeneLevelTable" {
			c.DungeonGeneLevelTable.bindRefs(c)
		}
		if tableName == "DungeonLordEquipLevelTable" {
			c.DungeonLordEquipLevelTable.bindRefs(c)
		}
		if tableName == "DungeonRefreshMonsterEventTable" {
			c.DungeonRefreshMonsterEventTable.bindRefs(c)
		}
		if tableName == "DungeonSunshineLevelTable" {
			c.DungeonSunshineLevelTable.bindRefs(c)
		}
		if tableName == "DungeonTypeTable" {
			c.DungeonTypeTable.bindRefs(c)
		}
		if tableName == "FunctionPreviewTable" {
			c.FunctionPreviewTable.bindRefs(c)
		}
		if tableName == "FunctionTable" {
			c.FunctionTable.bindRefs(c)
		}
		if tableName == "GameConfigs" {
			c.GameConfigs.bindRefs(c)
		}
		if tableName == "GemAffixQualityTable" {
			c.GemAffixQualityTable.bindRefs(c)
		}
		if tableName == "GemQualityTypeTable" {
			c.GemQualityTypeTable.bindRefs(c)
		}
		if tableName == "GoToTable" {
			c.GoToTable.bindRefs(c)
		}
		if tableName == "GuildFlagTable" {
			c.GuildFlagTable.bindRefs(c)
		}
		if tableName == "GuildHaggleTable" {
			c.GuildHaggleTable.bindRefs(c)
		}
		if tableName == "GuildLevelTable" {
			c.GuildLevelTable.bindRefs(c)
		}
		if tableName == "GuildPermissionTable" {
			c.GuildPermissionTable.bindRefs(c)
		}
		if tableName == "GuildRankTable" {
			c.GuildRankTable.bindRefs(c)
		}
		if tableName == "GuildShopTable" {
			c.GuildShopTable.bindRefs(c)
		}
		if tableName == "GuildTaskTable" {
			c.GuildTaskTable.bindRefs(c)
		}
		if tableName == "GuildTasksScoreTable" {
			c.GuildTasksScoreTable.bindRefs(c)
		}
		if tableName == "HeroBondsTable" {
			c.HeroBondsTable.bindRefs(c)
		}
		if tableName == "HeroCareerTable" {
			c.HeroCareerTable.bindRefs(c)
		}
		if tableName == "HeroConfigTable" {
			c.HeroConfigTable.bindRefs(c)
		}
		if tableName == "HeroFragmentTable" {
			c.HeroFragmentTable.bindRefs(c)
		}
		if tableName == "HeroGeneFragmentTable" {
			c.HeroGeneFragmentTable.bindRefs(c)
		}
		if tableName == "HeroGeneTable" {
			c.HeroGeneTable.bindRefs(c)
		}
		if tableName == "HeroLevelTable" {
			c.HeroLevelTable.bindRefs(c)
		}
		if tableName == "HeroLotteryGroupTable" {
			c.HeroLotteryGroupTable.bindRefs(c)
		}
		if tableName == "HeroLotteryMustTable" {
			c.HeroLotteryMustTable.bindRefs(c)
		}
		if tableName == "HeroLotteryRandomGroupTable" {
			c.HeroLotteryRandomGroupTable.bindRefs(c)
		}
		if tableName == "HeroLotteryRandomTable" {
			c.HeroLotteryRandomTable.bindRefs(c)
		}
		if tableName == "HeroQualityTable" {
			c.HeroQualityTable.bindRefs(c)
		}
		if tableName == "HeroRestrainTable" {
			c.HeroRestrainTable.bindRefs(c)
		}
		if tableName == "HeroSkillAttrTable" {
			c.HeroSkillAttrTable.bindRefs(c)
		}
		if tableName == "HeroSkillAwakeTable" {
			c.HeroSkillAwakeTable.bindRefs(c)
		}
		if tableName == "HeroSkillBuffTable" {
			c.HeroSkillBuffTable.bindRefs(c)
		}
		if tableName == "HeroSkillBuffTypeTable" {
			c.HeroSkillBuffTypeTable.bindRefs(c)
		}
		if tableName == "HeroSkillEffectTable" {
			c.HeroSkillEffectTable.bindRefs(c)
		}
		if tableName == "HeroSkillGroupTable" {
			c.HeroSkillGroupTable.bindRefs(c)
		}
		if tableName == "HeroSkillTypeTable" {
			c.HeroSkillTypeTable.bindRefs(c)
		}
		if tableName == "HeroStarTable" {
			c.HeroStarTable.bindRefs(c)
		}
		if tableName == "HeroTable" {
			c.HeroTable.bindRefs(c)
		}
		if tableName == "HeroTypeTable" {
			c.HeroTypeTable.bindRefs(c)
		}
		if tableName == "Iap1stTable" {
			c.Iap1stTable.bindRefs(c)
		}
		if tableName == "Iap2XTable" {
			c.Iap2XTable.bindRefs(c)
		}
		if tableName == "IapAdFreeTable" {
			c.IapAdFreeTable.bindRefs(c)
		}
		if tableName == "IapBPTable" {
			c.IapBPTable.bindRefs(c)
		}
		if tableName == "IapBpRewardTable" {
			c.IapBpRewardTable.bindRefs(c)
		}
		if tableName == "IapDailySaleFreeRewardTable" {
			c.IapDailySaleFreeRewardTable.bindRefs(c)
		}
		if tableName == "IapDailySaleRewardGroupTable" {
			c.IapDailySaleRewardGroupTable.bindRefs(c)
		}
		if tableName == "IapDailySaleRewardTable" {
			c.IapDailySaleRewardTable.bindRefs(c)
		}
		if tableName == "IapDailySaleTable" {
			c.IapDailySaleTable.bindRefs(c)
		}
		if tableName == "IapDealTable" {
			c.IapDealTable.bindRefs(c)
		}
		if tableName == "IapLevelFundRewardTable" {
			c.IapLevelFundRewardTable.bindRefs(c)
		}
		if tableName == "IapLevelFundTable" {
			c.IapLevelFundTable.bindRefs(c)
		}
		if tableName == "IapMonthCardTable" {
			c.IapMonthCardTable.bindRefs(c)
		}
		if tableName == "IapPackageDiamondShopTable" {
			c.IapPackageDiamondShopTable.bindRefs(c)
		}
		if tableName == "IapPackageRewardTable" {
			c.IapPackageRewardTable.bindRefs(c)
		}
		if tableName == "IapPackageTable" {
			c.IapPackageTable.bindRefs(c)
		}
		if tableName == "IapPriceTable" {
			c.IapPriceTable.bindRefs(c)
		}
		if tableName == "IapRegularPackGroupTable" {
			c.IapRegularPackGroupTable.bindRefs(c)
		}
		if tableName == "IapRegularPackTable" {
			c.IapRegularPackTable.bindRefs(c)
		}
		if tableName == "IapShopMallTable" {
			c.IapShopMallTable.bindRefs(c)
		}
		if tableName == "IapSignRewardTable" {
			c.IapSignRewardTable.bindRefs(c)
		}
		if tableName == "IapSignTable" {
			c.IapSignTable.bindRefs(c)
		}
		if tableName == "IapTriggerPackGroupTable" {
			c.IapTriggerPackGroupTable.bindRefs(c)
		}
		if tableName == "IapTriggerPackTable" {
			c.IapTriggerPackTable.bindRefs(c)
		}
		if tableName == "IapTurnPackTable" {
			c.IapTurnPackTable.bindRefs(c)
		}
		if tableName == "IdleMonsterTable" {
			c.IdleMonsterTable.bindRefs(c)
		}
		if tableName == "IdleRewardTable" {
			c.IdleRewardTable.bindRefs(c)
		}
		if tableName == "IdleRewardTime" {
			c.IdleRewardTime.bindRefs(c)
		}
		if tableName == "ItemQualityTable" {
			c.ItemQualityTable.bindRefs(c)
		}
		if tableName == "ItemSourceTable" {
			c.ItemSourceTable.bindRefs(c)
		}
		if tableName == "ItemTable" {
			c.ItemTable.bindRefs(c)
		}
		if tableName == "LanguageCnTable" {
			c.LanguageCnTable.bindRefs(c)
		}
		if tableName == "LoginOpenTable" {
			c.LoginOpenTable.bindRefs(c)
		}
		if tableName == "LordEquipGradeTable" {
			c.LordEquipGradeTable.bindRefs(c)
		}
		if tableName == "LordEquipGradeTypeTable" {
			c.LordEquipGradeTypeTable.bindRefs(c)
		}
		if tableName == "LordEquipTable" {
			c.LordEquipTable.bindRefs(c)
		}
		if tableName == "LordEquipTypeTable" {
			c.LordEquipTypeTable.bindRefs(c)
		}
		if tableName == "LordGemCraftTable" {
			c.LordGemCraftTable.bindRefs(c)
		}
		if tableName == "LordGemDropCntTable" {
			c.LordGemDropCntTable.bindRefs(c)
		}
		if tableName == "LordGemDropQualityTable" {
			c.LordGemDropQualityTable.bindRefs(c)
		}
		if tableName == "LordGemRandomGroupChanceTable" {
			c.LordGemRandomGroupChanceTable.bindRefs(c)
		}
		if tableName == "LordGemRandomGroupMustTable" {
			c.LordGemRandomGroupMustTable.bindRefs(c)
		}
		if tableName == "LordGemRandomGroupTable" {
			c.LordGemRandomGroupTable.bindRefs(c)
		}
		if tableName == "LordGemRandomRewardGroupTable" {
			c.LordGemRandomRewardGroupTable.bindRefs(c)
		}
		if tableName == "LordGemReforgeTable" {
			c.LordGemReforgeTable.bindRefs(c)
		}
		if tableName == "LordGemTable" {
			c.LordGemTable.bindRefs(c)
		}
		if tableName == "MailTable" {
			c.MailTable.bindRefs(c)
		}
		if tableName == "MainChapterLevelTable" {
			c.MainChapterLevelTable.bindRefs(c)
		}
		if tableName == "MainChapterTable" {
			c.MainChapterTable.bindRefs(c)
		}
		if tableName == "MainLevelPassRewardTable" {
			c.MainLevelPassRewardTable.bindRefs(c)
		}
		if tableName == "MainLevelRangeDmgTable" {
			c.MainLevelRangeDmgTable.bindRefs(c)
		}
		if tableName == "MainLevelRewardRatioTable" {
			c.MainLevelRewardRatioTable.bindRefs(c)
		}
		if tableName == "MainLevelRewardTable" {
			c.MainLevelRewardTable.bindRefs(c)
		}
		if tableName == "MainLevelRogueRewardWeightTable" {
			c.MainLevelRogueRewardWeightTable.bindRefs(c)
		}
		if tableName == "MainLevelTable" {
			c.MainLevelTable.bindRefs(c)
		}
		if tableName == "MainLineTasksTable" {
			c.MainLineTasksTable.bindRefs(c)
		}
		if tableName == "MapEventBuffTable" {
			c.MapEventBuffTable.bindRefs(c)
		}
		if tableName == "MapEventMonsterGroupTable" {
			c.MapEventMonsterGroupTable.bindRefs(c)
		}
		if tableName == "MapEventMonsterTable" {
			c.MapEventMonsterTable.bindRefs(c)
		}
		if tableName == "MapEventObstacleTable" {
			c.MapEventObstacleTable.bindRefs(c)
		}
		if tableName == "MapEventPropTable" {
			c.MapEventPropTable.bindRefs(c)
		}
		if tableName == "MapEventRewardTable" {
			c.MapEventRewardTable.bindRefs(c)
		}
		if tableName == "MapEventSkillTable" {
			c.MapEventSkillTable.bindRefs(c)
		}
		if tableName == "MapEventTable" {
			c.MapEventTable.bindRefs(c)
		}
		if tableName == "MapRefreshMonsterEventTable" {
			c.MapRefreshMonsterEventTable.bindRefs(c)
		}
		if tableName == "ModifierTable" {
			c.ModifierTable.bindRefs(c)
		}
		if tableName == "MonsterCareerTable" {
			c.MonsterCareerTable.bindRefs(c)
		}
		if tableName == "MonsterGradeTable" {
			c.MonsterGradeTable.bindRefs(c)
		}
		if tableName == "MonsterPosTypeTable" {
			c.MonsterPosTypeTable.bindRefs(c)
		}
		if tableName == "MonsterSkillTable" {
			c.MonsterSkillTable.bindRefs(c)
		}
		if tableName == "MonsterTable" {
			c.MonsterTable.bindRefs(c)
		}
		if tableName == "MonsterTypeTable" {
			c.MonsterTypeTable.bindRefs(c)
		}
		if tableName == "NewbieTable" {
			c.NewbieTable.bindRefs(c)
		}
		if tableName == "NpcDialogueTable" {
			c.NpcDialogueTable.bindRefs(c)
		}
		if tableName == "PhotovoltaicTable" {
			c.PhotovoltaicTable.bindRefs(c)
		}
		if tableName == "PresetsTable" {
			c.PresetsTable.bindRefs(c)
		}
		if tableName == "RankMainTable" {
			c.RankMainTable.bindRefs(c)
		}
		if tableName == "RankRewardTable" {
			c.RankRewardTable.bindRefs(c)
		}
		if tableName == "RougeRefreshTable" {
			c.RougeRefreshTable.bindRefs(c)
		}
		if tableName == "RougeTabEffectTable" {
			c.RougeTabEffectTable.bindRefs(c)
		}
		if tableName == "RougeTabGroupRandomTable" {
			c.RougeTabGroupRandomTable.bindRefs(c)
		}
		if tableName == "RougeTabGroupTable" {
			c.RougeTabGroupTable.bindRefs(c)
		}
		if tableName == "RougeTabNewbieTable" {
			c.RougeTabNewbieTable.bindRefs(c)
		}
		if tableName == "RougeTabTable" {
			c.RougeTabTable.bindRefs(c)
		}
		if tableName == "RougeWeightCoef" {
			c.RougeWeightCoef.bindRefs(c)
		}
		if tableName == "SelectChestGroupTable" {
			c.SelectChestGroupTable.bindRefs(c)
		}
		if tableName == "SelectChestMainTable" {
			c.SelectChestMainTable.bindRefs(c)
		}
		if tableName == "SevenDayTasksScoreTable" {
			c.SevenDayTasksScoreTable.bindRefs(c)
		}
		if tableName == "SevenDayTasksTable" {
			c.SevenDayTasksTable.bindRefs(c)
		}
		if tableName == "ShopTable" {
			c.ShopTable.bindRefs(c)
		}
		if tableName == "Sign7Table" {
			c.Sign7Table.bindRefs(c)
		}
		if tableName == "SkillDmgTypeTable" {
			c.SkillDmgTypeTable.bindRefs(c)
		}
		if tableName == "TowerAILevelTable" {
			c.TowerAILevelTable.bindRefs(c)
		}
		if tableName == "TowerAITable" {
			c.TowerAITable.bindRefs(c)
		}
		if tableName == "TowerTable" {
			c.TowerTable.bindRefs(c)
		}
		if tableName == "TurnRewardTable" {
			c.TurnRewardTable.bindRefs(c)
		}
		if tableName == "TurnScoreRewardTable" {
			c.TurnScoreRewardTable.bindRefs(c)
		}
		if tableName == "TurnTable" {
			c.TurnTable.bindRefs(c)
		}
		if tableName == "VehicleTable" {
			c.VehicleTable.bindRefs(c)
		}

	}
	return nil
}

var enumJsonContent string = `{
	"Enums": [
		{
			"EnumName": "AttrDefaultType",
			"Cases": [
				{
					"CaseName": "Number",
					"CaseValue": 1
				},
				{
					"CaseName": "Model",
					"CaseValue": 2
				},
				{
					"CaseName": "Buff",
					"CaseValue": 3
				},
				{
					"CaseName": "Effect",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "BenefitCalcFormula",
			"Cases": [
				{
					"CaseName": "Formula1",
					"CaseValue": 1
				},
				{
					"CaseName": "Formula2",
					"CaseValue": 2
				},
				{
					"CaseName": "Formula3",
					"CaseValue": 3
				},
				{
					"CaseName": "Formula4",
					"CaseValue": 4
				},
				{
					"CaseName": "Formula5",
					"CaseValue": 5
				},
				{
					"CaseName": "Formula6",
					"CaseValue": 6
				},
				{
					"CaseName": "Formula7",
					"CaseValue": 7
				},
				{
					"CaseName": "Formula8",
					"CaseValue": 8
				},
				{
					"CaseName": "Formula9",
					"CaseValue": 9
				}
			]
		},
		{
			"EnumName": "BuffOverlyingType",
			"Cases": [
				{
					"CaseName": "NoOverlying",
					"CaseValue": 1
				},
				{
					"CaseName": "TimeOverlying",
					"CaseValue": 2
				},
				{
					"CaseName": "EffectOverlying",
					"CaseValue": 3
				},
				{
					"CaseName": "EffectCover",
					"CaseValue": 4
				},
				{
					"CaseName": "EffectReplace",
					"CaseValue": 5
				},
				{
					"CaseName": "EffectMultiple",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "BuffTarget",
			"Cases": [
				{
					"CaseName": "Own",
					"CaseValue": 1
				},
				{
					"CaseName": "OwnSide",
					"CaseValue": 2
				},
				{
					"CaseName": "OwnSideHeroDefense",
					"CaseValue": 3
				},
				{
					"CaseName": "OwnSideHeroRanged",
					"CaseValue": 4
				},
				{
					"CaseName": "OwnSideHeroSupport",
					"CaseValue": 5
				},
				{
					"CaseName": "OwnSideHeroFront",
					"CaseValue": 6
				},
				{
					"CaseName": "OwnSideHeroBehind",
					"CaseValue": 7
				},
				{
					"CaseName": "OwnSideHeroHPLowest",
					"CaseValue": 8
				},
				{
					"CaseName": "Opposite",
					"CaseValue": 9
				},
				{
					"CaseName": "OppositeSide",
					"CaseValue": 10
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 11
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 12
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 13
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 14
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 15
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 16
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 17
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 18
				},
				{
					"CaseName": "Boss",
					"CaseValue": 19
				},
				{
					"CaseName": "Vehicle",
					"CaseValue": 20
				},
				{
					"CaseName": "OwnForward",
					"CaseValue": 21
				},
				{
					"CaseName": "OwnSideHeroMagic",
					"CaseValue": 22
				},
				{
					"CaseName": "OwnSideHeroSuperPowers",
					"CaseValue": 23
				},
				{
					"CaseName": "OwnSideHeroTech",
					"CaseValue": 24
				},
				{
					"CaseName": "OppositeSideHeroMagic",
					"CaseValue": 25
				},
				{
					"CaseName": "OppositeSideHeroSuperPowers",
					"CaseValue": 26
				},
				{
					"CaseName": "OppositeSideHeroTech",
					"CaseValue": 27
				},
				{
					"CaseName": "OwnSideHeroAtkHighest",
					"CaseValue": 28
				},
				{
					"CaseName": "OwnSideHeroBehindMagic",
					"CaseValue": 29
				},
				{
					"CaseName": "OwnSideHeroBehindTech",
					"CaseValue": 30
				},
				{
					"CaseName": "OwnSideHeroFrontDefenseRandom",
					"CaseValue": 31
				}
			]
		},
		{
			"EnumName": "BuffType",
			"Cases": [
				{
					"CaseName": "Buff",
					"CaseValue": 1
				},
				{
					"CaseName": "Debuff",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "CorrectType",
			"Cases": [
				{
					"CaseName": "Overlying",
					"CaseValue": 1
				},
				{
					"CaseName": "Cover",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "DungeonType",
			"Cases": [
				{
					"CaseName": "CoinDungeon",
					"CaseValue": 1
				},
				{
					"CaseName": "GeneDungeon",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipDungeon",
					"CaseValue": 3
				},
				{
					"CaseName": "SunshineDungeon",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "GemAffixQuality",
			"Cases": [
				{
					"CaseName": "GemAffixQuality1",
					"CaseValue": 1
				},
				{
					"CaseName": "GemAffixQuality2",
					"CaseValue": 2
				},
				{
					"CaseName": "GemAffixQuality3",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "GemQualityType",
			"Cases": [
				{
					"CaseName": "GemQualityType1",
					"CaseValue": 1
				},
				{
					"CaseName": "GemQualityType2",
					"CaseValue": 2
				},
				{
					"CaseName": "GemQualityType3",
					"CaseValue": 3
				},
				{
					"CaseName": "GemQualityType4",
					"CaseValue": 4
				},
				{
					"CaseName": "GemQualityType5",
					"CaseValue": 5
				},
				{
					"CaseName": "GemQualityType6",
					"CaseValue": 6
				},
				{
					"CaseName": "GemQualityType7",
					"CaseValue": 7
				}
			]
		},
		{
			"EnumName": "GuildFlagType",
			"Cases": [
				{
					"CaseName": "Base",
					"CaseValue": 1
				},
				{
					"CaseName": "Badge",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "GuildPermission",
			"Cases": [
				{
					"CaseName": "ChangeGuildFlag",
					"CaseValue": 1
				},
				{
					"CaseName": "ChangeGuildShortName",
					"CaseValue": 2
				},
				{
					"CaseName": "ChangeGuildName",
					"CaseValue": 3
				},
				{
					"CaseName": "EditNotice",
					"CaseValue": 4
				},
				{
					"CaseName": "ChangeRecruitSetting",
					"CaseValue": 5
				},
				{
					"CaseName": "ManageJoinApplication",
					"CaseValue": 6
				},
				{
					"CaseName": "DisbandGuild",
					"CaseValue": 7
				},
				{
					"CaseName": "TransferPresident",
					"CaseValue": 8
				},
				{
					"CaseName": "RemoveMember",
					"CaseValue": 9
				},
				{
					"CaseName": "ChangeMemberRank",
					"CaseValue": 10
				},
				{
					"CaseName": "ViewMemberInfo",
					"CaseValue": 11
				},
				{
					"CaseName": "ChangeRankTitle",
					"CaseValue": 12
				},
				{
					"CaseName": "ChangeLanguage",
					"CaseValue": 13
				},
				{
					"CaseName": "ExitGuild",
					"CaseValue": 14
				}
			]
		},
		{
			"EnumName": "GuildRank",
			"Cases": [
				{
					"CaseName": "Rank5",
					"CaseValue": 1
				},
				{
					"CaseName": "Rank4",
					"CaseValue": 2
				},
				{
					"CaseName": "Rank3",
					"CaseValue": 3
				},
				{
					"CaseName": "Rank2",
					"CaseValue": 4
				},
				{
					"CaseName": "Rank1",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroCareer",
			"Cases": [
				{
					"CaseName": "HeroDefense",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroRanged",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroSupport",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroConfig",
			"Cases": [
				{
					"CaseName": "HeroConfig1",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroConfig2",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroConfig3",
					"CaseValue": 3
				},
				{
					"CaseName": "HeroConfig4",
					"CaseValue": 4
				},
				{
					"CaseName": "HeroConfig5",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroQuality",
			"Cases": [
				{
					"CaseName": "HeroLegendary",
					"CaseValue": 1
				},
				{
					"CaseName": "HeroEpic",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroRare",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroSkillBuffType",
			"Cases": [
				{
					"CaseName": "Benefit",
					"CaseValue": 1
				},
				{
					"CaseName": "Silent",
					"CaseValue": 2
				},
				{
					"CaseName": "Stun",
					"CaseValue": 3
				},
				{
					"CaseName": "Paralysis",
					"CaseValue": 4
				},
				{
					"CaseName": "Sleep",
					"CaseValue": 5
				},
				{
					"CaseName": "Bind",
					"CaseValue": 6
				},
				{
					"CaseName": "Immortal",
					"CaseValue": 7
				},
				{
					"CaseName": "Veil",
					"CaseValue": 8
				},
				{
					"CaseName": "Stealth",
					"CaseValue": 9
				},
				{
					"CaseName": "Curse",
					"CaseValue": 10
				},
				{
					"CaseName": "Dot_bleed",
					"CaseValue": 11
				},
				{
					"CaseName": "Dot_poison",
					"CaseValue": 12
				},
				{
					"CaseName": "Dot_frostbite",
					"CaseValue": 13
				},
				{
					"CaseName": "Dot_burn",
					"CaseValue": 14
				},
				{
					"CaseName": "Block",
					"CaseValue": 15
				},
				{
					"CaseName": "Unrevive",
					"CaseValue": 16
				},
				{
					"CaseName": "EternalSlumber",
					"CaseValue": 17
				},
				{
					"CaseName": "Tense",
					"CaseValue": 18
				},
				{
					"CaseName": "Immunity",
					"CaseValue": 19
				},
				{
					"CaseName": "Shield",
					"CaseValue": 20
				},
				{
					"CaseName": "HalfAsleep",
					"CaseValue": 21
				},
				{
					"CaseName": "Nightmare",
					"CaseValue": 22
				},
				{
					"CaseName": "LifeSteal",
					"CaseValue": 23
				},
				{
					"CaseName": "Revive",
					"CaseValue": 24
				},
				{
					"CaseName": "HpRecovery",
					"CaseValue": 25
				},
				{
					"CaseName": "SkillSwitch",
					"CaseValue": 26
				},
				{
					"CaseName": "Taunted",
					"CaseValue": 27
				},
				{
					"CaseName": "Dmg",
					"CaseValue": 28
				},
				{
					"CaseName": "RemoveBuff",
					"CaseValue": 29
				},
				{
					"CaseName": "OnSideExplosion",
					"CaseValue": 30
				},
				{
					"CaseName": "Frozen",
					"CaseValue": 31
				},
				{
					"CaseName": "Repel",
					"CaseValue": 32
				},
				{
					"CaseName": "Pull",
					"CaseValue": 33
				},
				{
					"CaseName": "LightingStruck",
					"CaseValue": 34
				},
				{
					"CaseName": "Vulnerability",
					"CaseValue": 35
				},
				{
					"CaseName": "Lame",
					"CaseValue": 36
				},
				{
					"CaseName": "Rampage",
					"CaseValue": 37
				},
				{
					"CaseName": "BuffDelay",
					"CaseValue": 38
				},
				{
					"CaseName": "CoolingOff",
					"CaseValue": 39
				},
				{
					"CaseName": "RecoveryDown",
					"CaseValue": 40
				},
				{
					"CaseName": "Armour",
					"CaseValue": 41
				},
				{
					"CaseName": "FrozenHpRecovery",
					"CaseValue": 42
				},
				{
					"CaseName": "ElectrostaticSputtering",
					"CaseValue": 43
				},
				{
					"CaseName": "InjuryHealing",
					"CaseValue": 44
				},
				{
					"CaseName": "Summon",
					"CaseValue": 45
				},
				{
					"CaseName": "CrabWalk",
					"CaseValue": 46
				},
				{
					"CaseName": "Invulnerable",
					"CaseValue": 47
				},
				{
					"CaseName": "Split",
					"CaseValue": 48
				},
				{
					"CaseName": "Trigger",
					"CaseValue": 49
				},
				{
					"CaseName": "Excavation",
					"CaseValue": 50
				},
				{
					"CaseName": "TombStone",
					"CaseValue": 51
				},
				{
					"CaseName": "InstantDeath",
					"CaseValue": 52
				},
				{
					"CaseName": "RangeRampage",
					"CaseValue": 53
				},
				{
					"CaseName": "Dot_wind",
					"CaseValue": 54
				},
				{
					"CaseName": "ConditionTrigger",
					"CaseValue": 55
				},
				{
					"CaseName": "Immolate",
					"CaseValue": 56
				},
				{
					"CaseName": "HpSwitch",
					"CaseValue": 57
				},
				{
					"CaseName": "StepingStone",
					"CaseValue": 58
				},
				{
					"CaseName": "EDER",
					"CaseValue": 59
				},
				{
					"CaseName": "Shippuden",
					"CaseValue": 60
				}
			]
		},
		{
			"EnumName": "HeroSkillDmgType",
			"Cases": [
				{
					"CaseName": "DmgValue",
					"CaseValue": 1
				},
				{
					"CaseName": "DmgRatio",
					"CaseValue": 2
				},
				{
					"CaseName": "MaxHpPerDmg",
					"CaseValue": 3
				},
				{
					"CaseName": "CurHpPerDmg",
					"CaseValue": 4
				},
				{
					"CaseName": "LossHpPerDmg",
					"CaseValue": 5
				},
				{
					"CaseName": "InheritedSkillRatio",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "HeroSkillEffectType",
			"Cases": [
				{
					"CaseName": "Buff",
					"CaseValue": 1
				},
				{
					"CaseName": "GuideLaser",
					"CaseValue": 2
				},
				{
					"CaseName": "BulletFire",
					"CaseValue": 3
				},
				{
					"CaseName": "IceRose",
					"CaseValue": 4
				},
				{
					"CaseName": "BulletWind",
					"CaseValue": 5
				},
				{
					"CaseName": "Laser",
					"CaseValue": 6
				},
				{
					"CaseName": "ElectricFierce",
					"CaseValue": 7
				},
				{
					"CaseName": "Airdrop",
					"CaseValue": 8
				},
				{
					"CaseName": "ElectricArc",
					"CaseValue": 9
				},
				{
					"CaseName": "BulletIce",
					"CaseValue": 10
				},
				{
					"CaseName": "DragonFlame",
					"CaseValue": 11
				},
				{
					"CaseName": "Car",
					"CaseValue": 12
				},
				{
					"CaseName": "Shrapnel",
					"CaseValue": 13
				},
				{
					"CaseName": "Cyclone",
					"CaseValue": 14
				},
				{
					"CaseName": "BulletPea",
					"CaseValue": 15
				},
				{
					"CaseName": "HandSword",
					"CaseValue": 16
				},
				{
					"CaseName": "Missile",
					"CaseValue": 17
				},
				{
					"CaseName": "MeleeAtk",
					"CaseValue": 18
				},
				{
					"CaseName": "SummonMonster",
					"CaseValue": 19
				},
				{
					"CaseName": "Aoe",
					"CaseValue": 20
				},
				{
					"CaseName": "UnlockTabLevelLimit",
					"CaseValue": 21
				},
				{
					"CaseName": "GetTab",
					"CaseValue": 22
				},
				{
					"CaseName": "UnlockTab",
					"CaseValue": 23
				},
				{
					"CaseName": "HomelanderLaser",
					"CaseValue": 24
				},
				{
					"CaseName": "ReplacedTab",
					"CaseValue": 25
				},
				{
					"CaseName": "BossEarLaser",
					"CaseValue": 26
				},
				{
					"CaseName": "BossMouseLaser",
					"CaseValue": 27
				},
				{
					"CaseName": "Boss2",
					"CaseValue": 28
				},
				{
					"CaseName": "MultipleMeleeAtk",
					"CaseValue": 29
				},
				{
					"CaseName": "SuicideBombing",
					"CaseValue": 30
				},
				{
					"CaseName": "Phase2Boss1",
					"CaseValue": 31
				},
				{
					"CaseName": "Phase2Boss6",
					"CaseValue": 32
				}
			]
		},
		{
			"EnumName": "HeroSkillHpRecoveryType",
			"Cases": [
				{
					"CaseName": "HpRecoveryValue",
					"CaseValue": 1
				},
				{
					"CaseName": "MaxHpPerRecovery",
					"CaseValue": 2
				},
				{
					"CaseName": "CurHpPerRecovery",
					"CaseValue": 3
				},
				{
					"CaseName": "LossHpPerRecovery",
					"CaseValue": 4
				},
				{
					"CaseName": "DmgPerRecovery",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "HeroSkillRangePolygon",
			"Cases": [
				{
					"CaseName": "Rectangle",
					"CaseValue": 1
				},
				{
					"CaseName": "Circular",
					"CaseValue": 2
				},
				{
					"CaseName": "Sector",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroSkillType",
			"Cases": [
				{
					"CaseName": "HitSkill",
					"CaseValue": 1
				},
				{
					"CaseName": "NegativeSkill",
					"CaseValue": 2
				},
				{
					"CaseName": "GiftSkill",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "HeroType",
			"Cases": [
				{
					"CaseName": "Magic",
					"CaseValue": 1
				},
				{
					"CaseName": "SuperPowers",
					"CaseValue": 2
				},
				{
					"CaseName": "Tech",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "IapBoothType",
			"Cases": [
				{
					"CaseName": "DiamondShop",
					"CaseValue": 1
				},
				{
					"CaseName": "DailySale",
					"CaseValue": 2
				},
				{
					"CaseName": "RegularPack",
					"CaseValue": 3
				},
				{
					"CaseName": "RegularBp",
					"CaseValue": 4
				},
				{
					"CaseName": "NoAds",
					"CaseValue": 5
				},
				{
					"CaseName": "2X",
					"CaseValue": 6
				},
				{
					"CaseName": "MonthCard",
					"CaseValue": 7
				},
				{
					"CaseName": "Fund",
					"CaseValue": 8
				},
				{
					"CaseName": "Sign",
					"CaseValue": 9
				},
				{
					"CaseName": "TurnTable",
					"CaseValue": 10
				},
				{
					"CaseName": "Sign7",
					"CaseValue": 11
				}
			]
		},
		{
			"EnumName": "IapPackageType",
			"Cases": [
				{
					"CaseName": "Diamond",
					"CaseValue": 1
				},
				{
					"CaseName": "First",
					"CaseValue": 2
				},
				{
					"CaseName": "MonthCard",
					"CaseValue": 3
				},
				{
					"CaseName": "Fund",
					"CaseValue": 4
				},
				{
					"CaseName": "Regular",
					"CaseValue": 5
				},
				{
					"CaseName": "DailySale",
					"CaseValue": 6
				},
				{
					"CaseName": "AdFree",
					"CaseValue": 7
				},
				{
					"CaseName": "Life",
					"CaseValue": 8
				},
				{
					"CaseName": "SignBp",
					"CaseValue": 9
				},
				{
					"CaseName": "TurnActivity",
					"CaseValue": 10
				},
				{
					"CaseName": "Trigger",
					"CaseValue": 11
				}
			]
		},
		{
			"EnumName": "ItemType",
			"Cases": [
				{
					"CaseName": "Chest",
					"CaseValue": 1
				},
				{
					"CaseName": "ChestSelfSelect",
					"CaseValue": 2
				},
				{
					"CaseName": "Diamond",
					"CaseValue": 3
				},
				{
					"CaseName": "Doughnut",
					"CaseValue": 4
				},
				{
					"CaseName": "SunShine",
					"CaseValue": 5
				},
				{
					"CaseName": "SummonCard",
					"CaseValue": 6
				},
				{
					"CaseName": "SeedBagCommon",
					"CaseValue": 7
				},
				{
					"CaseName": "SeedBagRare",
					"CaseValue": 8
				},
				{
					"CaseName": "SeedBagEpic",
					"CaseValue": 9
				},
				{
					"CaseName": "SeedBagLegendary",
					"CaseValue": 10
				},
				{
					"CaseName": "SeedBagMyth",
					"CaseValue": 11
				},
				{
					"CaseName": "LegendarySkillBook",
					"CaseValue": 12
				},
				{
					"CaseName": "EpicSkillBook",
					"CaseValue": 13
				},
				{
					"CaseName": "RareSkillBook",
					"CaseValue": 14
				},
				{
					"CaseName": "UniversalLegendaryHeroFragment",
					"CaseValue": 15
				},
				{
					"CaseName": "UniversalEpicHeroFragment",
					"CaseValue": 16
				},
				{
					"CaseName": "UniversalRareHeroFragment",
					"CaseValue": 17
				},
				{
					"CaseName": "RandomLegendaryHeroFragment",
					"CaseValue": 18
				},
				{
					"CaseName": "RandomEpicHeroFragment",
					"CaseValue": 19
				},
				{
					"CaseName": "RandomRareHeroFragment",
					"CaseValue": 20
				},
				{
					"CaseName": "Hero",
					"CaseValue": 21
				},
				{
					"CaseName": "HeroFragment",
					"CaseValue": 22
				},
				{
					"CaseName": "Avatar",
					"CaseValue": 23
				},
				{
					"CaseName": "AvatarFrame",
					"CaseValue": 24
				},
				{
					"CaseName": "RougeExp",
					"CaseValue": 25
				},
				{
					"CaseName": "SkillBook",
					"CaseValue": 26
				},
				{
					"CaseName": "Energy",
					"CaseValue": 27
				},
				{
					"CaseName": "HeroGeneFragment",
					"CaseValue": 28
				},
				{
					"CaseName": "Coin",
					"CaseValue": 29
				},
				{
					"CaseName": "HeroGeneralGeneFragment",
					"CaseValue": 30
				},
				{
					"CaseName": "GemRandom",
					"CaseValue": 31
				},
				{
					"CaseName": "Gem",
					"CaseValue": 32
				},
				{
					"CaseName": "GemReforge",
					"CaseValue": 33
				},
				{
					"CaseName": "LordEquipManual",
					"CaseValue": 34
				},
				{
					"CaseName": "LordEquipRandomManual",
					"CaseValue": 35
				},
				{
					"CaseName": "GemDraw",
					"CaseValue": 36
				},
				{
					"CaseName": "GuildExp",
					"CaseValue": 37
				},
				{
					"CaseName": "GuildCoin",
					"CaseValue": 38
				},
				{
					"CaseName": "ArenaCoin",
					"CaseValue": 39
				},
				{
					"CaseName": "TowerKey",
					"CaseValue": 40
				},
				{
					"CaseName": "CoinDungeonKey",
					"CaseValue": 41
				},
				{
					"CaseName": "GeneDungeonKey",
					"CaseValue": 42
				},
				{
					"CaseName": "LordEquipDungenKey",
					"CaseValue": 43
				},
				{
					"CaseName": "LordEquipGradeUpManual",
					"CaseValue": 44
				},
				{
					"CaseName": "RegularBpKey1",
					"CaseValue": 45
				},
				{
					"CaseName": "FreeAd",
					"CaseValue": 46
				},
				{
					"CaseName": "2X",
					"CaseValue": 47
				},
				{
					"CaseName": "SignKey1",
					"CaseValue": 48
				},
				{
					"CaseName": "FundKey1",
					"CaseValue": 49
				},
				{
					"CaseName": "MonthCard1",
					"CaseValue": 50
				},
				{
					"CaseName": "MonthCard2",
					"CaseValue": 51
				},
				{
					"CaseName": "turntablecoin",
					"CaseValue": 52
				},
				{
					"CaseName": "LordEquipMaterial",
					"CaseValue": 53
				},
				{
					"CaseName": "HeroQualityUp",
					"CaseValue": 54
				},
				{
					"CaseName": "SevenDayTasksScore",
					"CaseValue": 55
				}
			]
		},
		{
			"EnumName": "LaserTarget",
			"Cases": [
				{
					"CaseName": "Opposite",
					"CaseValue": 1
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 2
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 3
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 4
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 5
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 6
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 7
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 8
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 9
				},
				{
					"CaseName": "Boss",
					"CaseValue": 10
				},
				{
					"CaseName": "OwnForward",
					"CaseValue": 11
				}
			]
		},
		{
			"EnumName": "LevelType",
			"Cases": [
				{
					"CaseName": "TowerDefense",
					"CaseValue": 1
				},
				{
					"CaseName": "ParkOur",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "LogicType",
			"Cases": [
				{
					"CaseName": "And",
					"CaseValue": 1
				},
				{
					"CaseName": "Or",
					"CaseValue": 2
				},
				{
					"CaseName": "Invert",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "LordEquipGradeType",
			"Cases": [
				{
					"CaseName": "LordEquipGrade1",
					"CaseValue": 1
				},
				{
					"CaseName": "LordEquipGrade2",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipGrade3",
					"CaseValue": 3
				},
				{
					"CaseName": "LordEquipGrade4",
					"CaseValue": 4
				},
				{
					"CaseName": "LordEquipGrade5",
					"CaseValue": 5
				},
				{
					"CaseName": "LordEquipGrade6",
					"CaseValue": 6
				},
				{
					"CaseName": "LordEquipGrade7",
					"CaseValue": 7
				}
			]
		},
		{
			"EnumName": "LordEquipType",
			"Cases": [
				{
					"CaseName": "LordEquipType1",
					"CaseValue": 1
				},
				{
					"CaseName": "LordEquipType2",
					"CaseValue": 2
				},
				{
					"CaseName": "LordEquipType3",
					"CaseValue": 3
				},
				{
					"CaseName": "LordEquipType4",
					"CaseValue": 4
				},
				{
					"CaseName": "LordEquipType5",
					"CaseValue": 5
				},
				{
					"CaseName": "LordEquipType6",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "MapEventType",
			"Cases": [
				{
					"CaseName": "Monster",
					"CaseValue": 1
				},
				{
					"CaseName": "Prop",
					"CaseValue": 2
				},
				{
					"CaseName": "Buff",
					"CaseValue": 3
				},
				{
					"CaseName": "Obstacle",
					"CaseValue": 4
				},
				{
					"CaseName": "Reward",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "MissileTarget",
			"Cases": [
				{
					"CaseName": "Opposite",
					"CaseValue": 1
				},
				{
					"CaseName": "OppositeSideHeroDefense",
					"CaseValue": 2
				},
				{
					"CaseName": "OppositeSideHeroRanged",
					"CaseValue": 3
				},
				{
					"CaseName": "OppositeSideHeroSupport",
					"CaseValue": 4
				},
				{
					"CaseName": "OppoSideHeroFront",
					"CaseValue": 5
				},
				{
					"CaseName": "OppoSideHeroBehind",
					"CaseValue": 6
				},
				{
					"CaseName": "OppositeSideHeroHpLowest",
					"CaseValue": 7
				},
				{
					"CaseName": "PreSkillEffectTarget",
					"CaseValue": 8
				},
				{
					"CaseName": "PreSkillEffectTargetElseBoss",
					"CaseValue": 9
				},
				{
					"CaseName": "Boss",
					"CaseValue": 10
				}
			]
		},
		{
			"EnumName": "MonsterCareerType",
			"Cases": [
				{
					"CaseName": "Melee",
					"CaseValue": 1
				},
				{
					"CaseName": "Ranger",
					"CaseValue": 2
				},
				{
					"CaseName": "Tank",
					"CaseValue": 3
				},
				{
					"CaseName": "Assassin",
					"CaseValue": 4
				},
				{
					"CaseName": "AirMelee",
					"CaseValue": 5
				},
				{
					"CaseName": "AirRanger",
					"CaseValue": 6
				},
				{
					"CaseName": "Suicide",
					"CaseValue": 7
				},
				{
					"CaseName": "LongRanger",
					"CaseValue": 8
				}
			]
		},
		{
			"EnumName": "MonsterGrade",
			"Cases": [
				{
					"CaseName": "Common",
					"CaseValue": 1
				},
				{
					"CaseName": "Elite",
					"CaseValue": 2
				},
				{
					"CaseName": "Boss",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "MonsterPosType",
			"Cases": [
				{
					"CaseName": "Ground",
					"CaseValue": 1
				},
				{
					"CaseName": "Air",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "MonsterRefreshType",
			"Cases": [
				{
					"CaseName": "InitialRefresh",
					"CaseValue": 1
				},
				{
					"CaseName": "DelayRefresh",
					"CaseValue": 2
				},
				{
					"CaseName": "UpstreamDeathCntRefresh",
					"CaseValue": 3
				},
				{
					"CaseName": "PassPlotsRefresh",
					"CaseValue": 4
				},
				{
					"CaseName": "AfterRefreshing",
					"CaseValue": 5
				},
				{
					"CaseName": "AfterTheGameStarts",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "PurchaseLimitType",
			"Cases": [
				{
					"CaseName": "DailyLimit",
					"CaseValue": 1
				},
				{
					"CaseName": "WeeklyLimit",
					"CaseValue": 2
				},
				{
					"CaseName": "MonthlyLimit",
					"CaseValue": 3
				},
				{
					"CaseName": "LifeLimit",
					"CaseValue": 4
				},
				{
					"CaseName": "UnLimit",
					"CaseValue": 5
				}
			]
		},
		{
			"EnumName": "RougeTabType",
			"Cases": [
				{
					"CaseName": "EffectTab",
					"CaseValue": 1
				},
				{
					"CaseName": "UnlockTab",
					"CaseValue": 2
				},
				{
					"CaseName": "ConfigTab",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "ShopType",
			"Cases": [
				{
					"CaseName": "GuildShop",
					"CaseValue": 1
				},
				{
					"CaseName": "ArenaShop",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "SkillAttrOverlyingType",
			"Cases": [
				{
					"CaseName": "AddOverlying",
					"CaseValue": 1
				},
				{
					"CaseName": "MulOverlying",
					"CaseValue": 2
				},
				{
					"CaseName": "EnumOverlying",
					"CaseValue": 3
				},
				{
					"CaseName": "NoOverlying",
					"CaseValue": 4
				}
			]
		},
		{
			"EnumName": "SkillDmgType",
			"Cases": [
				{
					"CaseName": "Electrical",
					"CaseValue": 1
				},
				{
					"CaseName": "Wind",
					"CaseValue": 2
				},
				{
					"CaseName": "Light",
					"CaseValue": 3
				},
				{
					"CaseName": "Fire",
					"CaseValue": 4
				},
				{
					"CaseName": "Ice",
					"CaseValue": 5
				},
				{
					"CaseName": "Physical",
					"CaseValue": 6
				}
			]
		},
		{
			"EnumName": "SkillType",
			"Cases": [
				{
					"CaseName": "ActiveSkill",
					"CaseValue": 1
				},
				{
					"CaseName": "PassiveSkill",
					"CaseValue": 2
				},
				{
					"CaseName": "AuraSkill",
					"CaseValue": 3
				}
			]
		},
		{
			"EnumName": "TaskCounterType",
			"Cases": [
				{
					"CaseName": "Reset",
					"CaseValue": 1
				},
				{
					"CaseName": "Total",
					"CaseValue": 2
				}
			]
		},
		{
			"EnumName": "TaskType",
			"Cases": [
				{
					"CaseName": "Login",
					"CaseValue": 1
				},
				{
					"CaseName": "TotalLogin",
					"CaseValue": 2
				},
				{
					"CaseName": "LevelBegin",
					"CaseValue": 3
				},
				{
					"CaseName": "LevelPass",
					"CaseValue": 4
				},
				{
					"CaseName": "LevelPassTo",
					"CaseValue": 5
				},
				{
					"CaseName": "ItemBurn",
					"CaseValue": 6
				},
				{
					"CaseName": "TotalItemBurn",
					"CaseValue": 7
				},
				{
					"CaseName": "HeroLevelUp",
					"CaseValue": 8
				},
				{
					"CaseName": "HeroLevelUpTo",
					"CaseValue": 9
				},
				{
					"CaseName": "HeroStarUp",
					"CaseValue": 10
				},
				{
					"CaseName": "HeroStarUpTo",
					"CaseValue": 11
				},
				{
					"CaseName": "HeroSkillUp",
					"CaseValue": 12
				},
				{
					"CaseName": "HeroSkillUpTo",
					"CaseValue": 13
				},
				{
					"CaseName": "HeroGeneUp",
					"CaseValue": 14
				},
				{
					"CaseName": "HeroGeneUpTo",
					"CaseValue": 15
				},
				{
					"CaseName": "KillMonster",
					"CaseValue": 16
				},
				{
					"CaseName": "TotalKillMonster",
					"CaseValue": 17
				},
				{
					"CaseName": "claim_idle_reward",
					"CaseValue": 18
				},
				{
					"CaseName": "claim_pass_level_reward",
					"CaseValue": 19
				},
				{
					"CaseName": "Chat",
					"CaseValue": 20
				},
				{
					"CaseName": "Nigger",
					"CaseValue": 21
				},
				{
					"CaseName": "Rename",
					"CaseValue": 22
				},
				{
					"CaseName": "Avatar",
					"CaseValue": 23
				},
				{
					"CaseName": "JoinGuild",
					"CaseValue": 24
				},
				{
					"CaseName": "Sweep",
					"CaseValue": 25
				},
				{
					"CaseName": "HeroSummon",
					"CaseValue": 26
				},
				{
					"CaseName": "HeroConfig",
					"CaseValue": 27
				},
				{
					"CaseName": "LordEquipLvlUp",
					"CaseValue": 28
				},
				{
					"CaseName": "LordEquipLvlUpTo",
					"CaseValue": 29
				},
				{
					"CaseName": "GemCraft",
					"CaseValue": 30
				},
				{
					"CaseName": "GemSummon",
					"CaseValue": 31
				},
				{
					"CaseName": "Shopping",
					"CaseValue": 32
				},
				{
					"CaseName": "DungeonChallenge",
					"CaseValue": 33
				},
				{
					"CaseName": "DungeonSweep",
					"CaseValue": 34
				},
				{
					"CaseName": "ArenaChallenge",
					"CaseValue": 35
				},
				{
					"CaseName": "TotalActivateHero",
					"CaseValue": 36
				}
			]
		},
		{
			"EnumName": "TriggerPackType",
			"Cases": [
				{
					"CaseName": "LevelPass",
					"CaseValue": 1
				},
				{
					"CaseName": "GemDraw",
					"CaseValue": 2
				},
				{
					"CaseName": "HeroSummon",
					"CaseValue": 3
				}
			]
		}
	]
}`
