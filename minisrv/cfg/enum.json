{"Enums": [{"EnumName": "AttrDefaultType", "Cases": [{"CaseName": "Number", "CaseValue": 1}, {"CaseName": "Model", "CaseValue": 2}, {"CaseName": "Buff", "CaseValue": 3}, {"CaseName": "Effect", "CaseValue": 4}]}, {"EnumName": "BenefitCalcFormula", "Cases": [{"CaseName": "Formula1", "CaseValue": 1}, {"CaseName": "Formula2", "CaseValue": 2}, {"CaseName": "Formula3", "CaseValue": 3}, {"CaseName": "Formula4", "CaseValue": 4}, {"CaseName": "Formula5", "CaseValue": 5}, {"CaseName": "Formula6", "CaseValue": 6}, {"CaseName": "Formula7", "CaseValue": 7}, {"CaseName": "Formula8", "CaseValue": 8}, {"CaseName": "Formula9", "CaseValue": 9}]}, {"EnumName": "BuffOverlyingType", "Cases": [{"CaseName": "NoOverlying", "CaseValue": 1}, {"CaseName": "TimeOverlying", "CaseValue": 2}, {"CaseName": "EffectOverlying", "CaseValue": 3}, {"CaseName": "EffectCover", "CaseValue": 4}, {"CaseName": "EffectReplace", "CaseValue": 5}, {"CaseName": "EffectMultiple", "CaseValue": 6}]}, {"EnumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cases": [{"CaseName": "Own", "CaseValue": 1}, {"CaseName": "OwnSide", "CaseValue": 2}, {"CaseName": "OwnSideHeroDefense", "CaseValue": 3}, {"CaseName": "OwnSideHeroRanged", "CaseValue": 4}, {"CaseName": "OwnSideHeroSupport", "CaseValue": 5}, {"CaseName": "OwnSideHeroFront", "CaseValue": 6}, {"CaseName": "OwnSideHeroBehind", "CaseValue": 7}, {"CaseName": "OwnSideHeroHPLowest", "CaseValue": 8}, {"CaseName": "Opposite", "CaseValue": 9}, {"CaseName": "OppositeSide", "CaseValue": 10}, {"CaseName": "OppositeSideHeroDefense", "CaseValue": 11}, {"CaseName": "OppositeSideHeroRanged", "CaseValue": 12}, {"CaseName": "OppositeSideHeroSupport", "CaseValue": 13}, {"CaseName": "OppoSideHeroFront", "CaseValue": 14}, {"CaseName": "OppoSideHeroBehind", "CaseValue": 15}, {"CaseName": "OppositeSideHeroHpLowest", "CaseValue": 16}, {"CaseName": "PreSkillEffectTarget", "CaseValue": 17}, {"CaseName": "PreSkillEffectTargetElseBoss", "CaseValue": 18}, {"CaseName": "Boss", "CaseValue": 19}, {"CaseName": "Vehicle", "CaseValue": 20}, {"CaseName": "OwnForward", "CaseValue": 21}, {"CaseName": "OwnSideHeroMagic", "CaseValue": 22}, {"CaseName": "OwnSideHeroSuperPowers", "CaseValue": 23}, {"CaseName": "OwnSideHeroTech", "CaseValue": 24}, {"CaseName": "OppositeSideHeroMagic", "CaseValue": 25}, {"CaseName": "OppositeSideHeroSuperPowers", "CaseValue": 26}, {"CaseName": "OppositeSideHeroTech", "CaseValue": 27}, {"CaseName": "OwnSideHeroAtkHighest", "CaseValue": 28}, {"CaseName": "OwnSideHeroBehindMagic", "CaseValue": 29}, {"CaseName": "OwnSideHeroBehindTech", "CaseValue": 30}, {"CaseName": "OwnSideHeroFrontDefenseRandom", "CaseValue": 31}]}, {"EnumName": "BuffType", "Cases": [{"CaseName": "Buff", "CaseValue": 1}, {"CaseName": "<PERSON><PERSON><PERSON>", "CaseValue": 2}]}, {"EnumName": "CorrectType", "Cases": [{"CaseName": "Overlying", "CaseValue": 1}, {"CaseName": "Cover", "CaseValue": 2}]}, {"EnumName": "DungeonType", "Cases": [{"CaseName": "CoinDungeon", "CaseValue": 1}, {"CaseName": "GeneDungeon", "CaseValue": 2}, {"CaseName": "LordEquipDungeon", "CaseValue": 3}, {"CaseName": "SunshineDungeon", "CaseValue": 4}]}, {"EnumName": "GemAffixQuality", "Cases": [{"CaseName": "GemAffixQuality1", "CaseValue": 1}, {"CaseName": "GemAffixQuality2", "CaseValue": 2}, {"CaseName": "GemAffixQuality3", "CaseValue": 3}]}, {"EnumName": "GemQualityType", "Cases": [{"CaseName": "GemQualityType1", "CaseValue": 1}, {"CaseName": "GemQualityType2", "CaseValue": 2}, {"CaseName": "GemQualityType3", "CaseValue": 3}, {"CaseName": "GemQualityType4", "CaseValue": 4}, {"CaseName": "GemQualityType5", "CaseValue": 5}, {"CaseName": "GemQualityType6", "CaseValue": 6}, {"CaseName": "GemQualityType7", "CaseValue": 7}]}, {"EnumName": "GuildFlagType", "Cases": [{"CaseName": "Base", "CaseValue": 1}, {"CaseName": "Badge", "CaseValue": 2}]}, {"EnumName": "GuildPermission", "Cases": [{"CaseName": "ChangeGuildFlag", "CaseValue": 1}, {"CaseName": "ChangeGuildShortName", "CaseValue": 2}, {"CaseName": "ChangeGuildName", "CaseValue": 3}, {"CaseName": "EditNotice", "CaseValue": 4}, {"CaseName": "ChangeRecruitSetting", "CaseValue": 5}, {"CaseName": "ManageJoinApplication", "CaseValue": 6}, {"CaseName": "DisbandGuild", "CaseValue": 7}, {"CaseName": "TransferPresident", "CaseValue": 8}, {"CaseName": "RemoveMember", "CaseValue": 9}, {"CaseName": "ChangeMemberRank", "CaseValue": 10}, {"CaseName": "ViewMemberInfo", "CaseValue": 11}, {"CaseName": "ChangeRankTitle", "CaseValue": 12}, {"CaseName": "ChangeLanguage", "CaseValue": 13}, {"CaseName": "ExitGuild", "CaseValue": 14}]}, {"EnumName": "GuildRank", "Cases": [{"CaseName": "Rank5", "CaseValue": 1}, {"CaseName": "Rank4", "CaseValue": 2}, {"CaseName": "Rank3", "CaseValue": 3}, {"CaseName": "Rank2", "CaseValue": 4}, {"CaseName": "Rank1", "CaseValue": 5}]}, {"EnumName": "Hero<PERSON><PERSON><PERSON>", "Cases": [{"CaseName": "HeroDefense", "CaseValue": 1}, {"CaseName": "HeroRanged", "CaseValue": 2}, {"CaseName": "HeroSupport", "CaseValue": 3}]}, {"EnumName": "HeroConfig", "Cases": [{"CaseName": "HeroConfig1", "CaseValue": 1}, {"CaseName": "HeroConfig2", "CaseValue": 2}, {"CaseName": "HeroConfig3", "CaseValue": 3}, {"CaseName": "HeroConfig4", "CaseValue": 4}, {"CaseName": "HeroConfig5", "CaseValue": 5}]}, {"EnumName": "HeroQuality", "Cases": [{"CaseName": "HeroLegendary", "CaseValue": 1}, {"CaseName": "HeroEpic", "CaseValue": 2}, {"CaseName": "HeroRare", "CaseValue": 3}]}, {"EnumName": "HeroSkillBuffType", "Cases": [{"CaseName": "Benefit", "CaseValue": 1}, {"CaseName": "Silent", "CaseValue": 2}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 3}, {"CaseName": "Paralysis", "CaseValue": 4}, {"CaseName": "Sleep", "CaseValue": 5}, {"CaseName": "Bind", "CaseValue": 6}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON>", "CaseValue": 7}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 8}, {"CaseName": "Stealth", "CaseValue": 9}, {"CaseName": "Curse", "CaseValue": 10}, {"CaseName": "Dot_bleed", "CaseValue": 11}, {"CaseName": "Dot_poison", "CaseValue": 12}, {"CaseName": "Dot_frostbite", "CaseValue": 13}, {"CaseName": "Dot_burn", "CaseValue": 14}, {"CaseName": "Block", "CaseValue": 15}, {"CaseName": "Unrevive", "CaseValue": 16}, {"CaseName": "EternalSlumber", "CaseValue": 17}, {"CaseName": "<PERSON>se", "CaseValue": 18}, {"CaseName": "Immunity", "CaseValue": 19}, {"CaseName": "Shield", "CaseValue": 20}, {"CaseName": "HalfAsleep", "CaseValue": 21}, {"CaseName": "Nightmare", "CaseValue": 22}, {"CaseName": "LifeSteal", "CaseValue": 23}, {"CaseName": "Revive", "CaseValue": 24}, {"CaseName": "HpRecovery", "CaseValue": 25}, {"CaseName": "SkillSwitch", "CaseValue": 26}, {"CaseName": "Taunted", "CaseValue": 27}, {"CaseName": "Dmg", "CaseValue": 28}, {"CaseName": "RemoveBuff", "CaseValue": 29}, {"CaseName": "OnSideExplosion", "CaseValue": 30}, {"CaseName": "Frozen", "CaseValue": 31}, {"CaseName": "Repel", "CaseValue": 32}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 33}, {"CaseName": "LightingStruck", "CaseValue": 34}, {"CaseName": "Vulnerability", "CaseValue": 35}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 36}, {"CaseName": "Rampage", "CaseValue": 37}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CaseValue": 38}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON>", "CaseValue": 39}, {"CaseName": "RecoveryDown", "CaseValue": 40}, {"CaseName": "Armour", "CaseValue": 41}, {"CaseName": "FrozenHpRecovery", "CaseValue": 42}, {"CaseName": "ElectrostaticSputtering", "CaseValue": 43}, {"CaseName": "InjuryHealing", "CaseValue": 44}, {"CaseName": "<PERSON><PERSON><PERSON>", "CaseValue": 45}, {"CaseName": "CrabWalk", "CaseValue": 46}, {"CaseName": "Invulnerable", "CaseValue": 47}, {"CaseName": "Split", "CaseValue": 48}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 49}, {"CaseName": "Excavation", "CaseValue": 50}, {"CaseName": "TombStone", "CaseValue": 51}, {"CaseName": "InstantDeath", "CaseValue": 52}, {"CaseName": "RangeRampage", "CaseValue": 53}, {"CaseName": "Dot_wind", "CaseValue": 54}, {"CaseName": "ConditionTrigger", "CaseValue": 55}, {"CaseName": "Immolate", "CaseValue": 56}, {"CaseName": "HpSwitch", "CaseValue": 57}, {"CaseName": "StepingStone", "CaseValue": 58}, {"CaseName": "EDER", "CaseValue": 59}, {"CaseName": "Shippuden", "CaseValue": 60}]}, {"EnumName": "HeroSkillDmgType", "Cases": [{"CaseName": "DmgValue", "CaseValue": 1}, {"CaseName": "DmgRatio", "CaseValue": 2}, {"CaseName": "MaxHpPerDmg", "CaseValue": 3}, {"CaseName": "CurHpPerDmg", "CaseValue": 4}, {"CaseName": "LossHpPerDmg", "CaseValue": 5}, {"CaseName": "InheritedSkillRatio", "CaseValue": 6}]}, {"EnumName": "HeroSkillEffectType", "Cases": [{"CaseName": "Buff", "CaseValue": 1}, {"CaseName": "GuideLaser", "CaseValue": 2}, {"CaseName": "BulletFire", "CaseValue": 3}, {"CaseName": "IceRose", "CaseValue": 4}, {"CaseName": "BulletWind", "CaseValue": 5}, {"CaseName": "Laser", "CaseValue": 6}, {"CaseName": "ElectricFierce", "CaseValue": 7}, {"CaseName": "Airdrop", "CaseValue": 8}, {"CaseName": "ElectricArc", "CaseValue": 9}, {"CaseName": "BulletIce", "CaseValue": 10}, {"CaseName": "DragonFlame", "CaseValue": 11}, {"CaseName": "Car", "CaseValue": 12}, {"CaseName": "Shrapnel", "CaseValue": 13}, {"CaseName": "Cyclone", "CaseValue": 14}, {"CaseName": "BulletPea", "CaseValue": 15}, {"CaseName": "HandSword", "CaseValue": 16}, {"CaseName": "Missile", "CaseValue": 17}, {"CaseName": "MeleeAtk", "CaseValue": 18}, {"CaseName": "SummonMonster", "CaseValue": 19}, {"CaseName": "A<PERSON>", "CaseValue": 20}, {"CaseName": "UnlockTabLevelLimit", "CaseValue": 21}, {"CaseName": "GetTab", "CaseValue": 22}, {"CaseName": "UnlockTab", "CaseValue": 23}, {"CaseName": "Homelander<PERSON>ase<PERSON>", "CaseValue": 24}, {"CaseName": "ReplacedTab", "CaseValue": 25}, {"CaseName": "BossEarLaser", "CaseValue": 26}, {"CaseName": "BossMouseLaser", "CaseValue": 27}, {"CaseName": "Boss2", "CaseValue": 28}, {"CaseName": "MultipleMeleeAtk", "CaseValue": 29}, {"CaseName": "SuicideBombing", "CaseValue": 30}, {"CaseName": "Phase2Boss1", "CaseValue": 31}, {"CaseName": "Phase2Boss6", "CaseValue": 32}]}, {"EnumName": "HeroSkillHpRecoveryType", "Cases": [{"CaseName": "HpRecoveryValue", "CaseValue": 1}, {"CaseName": "MaxHpPerRecovery", "CaseValue": 2}, {"CaseName": "CurHpPerRecovery", "CaseValue": 3}, {"CaseName": "LossHpPerRecovery", "CaseValue": 4}, {"CaseName": "DmgPerRecovery", "CaseValue": 5}]}, {"EnumName": "HeroSkillRangePolygon", "Cases": [{"CaseName": "Rectangle", "CaseValue": 1}, {"CaseName": "Circular", "CaseValue": 2}, {"CaseName": "Sector", "CaseValue": 3}]}, {"EnumName": "HeroSkillType", "Cases": [{"CaseName": "HitSkill", "CaseValue": 1}, {"CaseName": "NegativeSkill", "CaseValue": 2}, {"CaseName": "GiftSkill", "CaseValue": 3}]}, {"EnumName": "HeroType", "Cases": [{"CaseName": "Magic", "CaseValue": 1}, {"CaseName": "SuperPowers", "CaseValue": 2}, {"CaseName": "Tech", "CaseValue": 3}]}, {"EnumName": "IapBoothType", "Cases": [{"CaseName": "DiamondShop", "CaseValue": 1}, {"CaseName": "DailySale", "CaseValue": 2}, {"CaseName": "RegularPack", "CaseValue": 3}, {"CaseName": "RegularBp", "CaseValue": 4}, {"CaseName": "NoAds", "CaseValue": 5}, {"CaseName": "2X", "CaseValue": 6}, {"CaseName": "MonthCard", "CaseValue": 7}, {"CaseName": "Fund", "CaseValue": 8}, {"CaseName": "Sign", "CaseValue": 9}, {"CaseName": "TurnTable", "CaseValue": 10}, {"CaseName": "Sign7", "CaseValue": 11}]}, {"EnumName": "IapPackageType", "Cases": [{"CaseName": "Diamond", "CaseValue": 1}, {"CaseName": "First", "CaseValue": 2}, {"CaseName": "MonthCard", "CaseValue": 3}, {"CaseName": "Fund", "CaseValue": 4}, {"CaseName": "Regular", "CaseValue": 5}, {"CaseName": "DailySale", "CaseValue": 6}, {"CaseName": "AdFree", "CaseValue": 7}, {"CaseName": "Life", "CaseValue": 8}, {"CaseName": "SignBp", "CaseValue": 9}, {"CaseName": "TurnActivity", "CaseValue": 10}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 11}]}, {"EnumName": "ItemType", "Cases": [{"CaseName": "Chest", "CaseValue": 1}, {"CaseName": "ChestSelfSelect", "CaseValue": 2}, {"CaseName": "Diamond", "CaseValue": 3}, {"CaseName": "Doughnut", "CaseValue": 4}, {"CaseName": "SunShine", "CaseValue": 5}, {"CaseName": "SummonCard", "CaseValue": 6}, {"CaseName": "SeedBagCommon", "CaseValue": 7}, {"CaseName": "SeedBagRare", "CaseValue": 8}, {"CaseName": "SeedBagEpic", "CaseValue": 9}, {"CaseName": "SeedBagLegendary", "CaseValue": 10}, {"CaseName": "SeedBagMyth", "CaseValue": 11}, {"CaseName": "LegendarySkillBook", "CaseValue": 12}, {"CaseName": "EpicSkillBook", "CaseValue": 13}, {"CaseName": "RareSkillBook", "CaseValue": 14}, {"CaseName": "UniversalLegendaryHeroFragment", "CaseValue": 15}, {"CaseName": "UniversalEpicHeroFragment", "CaseValue": 16}, {"CaseName": "UniversalRareHeroFragment", "CaseValue": 17}, {"CaseName": "RandomLegendaryHeroFragment", "CaseValue": 18}, {"CaseName": "RandomEpicHeroFragment", "CaseValue": 19}, {"CaseName": "RandomRareHeroFragment", "CaseValue": 20}, {"CaseName": "Hero", "CaseValue": 21}, {"CaseName": "HeroFragment", "CaseValue": 22}, {"CaseName": "Avatar", "CaseValue": 23}, {"CaseName": "AvatarFrame", "CaseValue": 24}, {"CaseName": "RougeExp", "CaseValue": 25}, {"CaseName": "SkillBook", "CaseValue": 26}, {"CaseName": "Energy", "CaseValue": 27}, {"CaseName": "HeroGeneFragment", "CaseValue": 28}, {"CaseName": "Coin", "CaseValue": 29}, {"CaseName": "HeroGeneralGeneFragment", "CaseValue": 30}, {"CaseName": "GemRandom", "CaseValue": 31}, {"CaseName": "Gem", "CaseValue": 32}, {"CaseName": "GemReforge", "CaseValue": 33}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CaseValue": 34}, {"CaseName": "LordEquip<PERSON>andom<PERSON><PERSON><PERSON>", "CaseValue": 35}, {"CaseName": "GemDraw", "CaseValue": 36}, {"CaseName": "GuildExp", "CaseValue": 37}, {"CaseName": "GuildCoin", "CaseValue": 38}, {"CaseName": "ArenaCoin", "CaseValue": 39}, {"CaseName": "TowerKey", "CaseValue": 40}, {"CaseName": "CoinDungeonKey", "CaseValue": 41}, {"CaseName": "GeneDungeonKey", "CaseValue": 42}, {"CaseName": "LordEquipD<PERSON><PERSON><PERSON>", "CaseValue": 43}, {"CaseName": "LordEquipGradeUpManual", "CaseValue": 44}, {"CaseName": "RegularBpKey1", "CaseValue": 45}, {"CaseName": "FreeAd", "CaseValue": 46}, {"CaseName": "2X", "CaseValue": 47}, {"CaseName": "SignKey1", "CaseValue": 48}, {"CaseName": "FundKey1", "CaseValue": 49}, {"CaseName": "MonthCard1", "CaseValue": 50}, {"CaseName": "MonthCard2", "CaseValue": 51}, {"CaseName": "turntablecoin", "CaseValue": 52}, {"CaseName": "LordEquipMaterial", "CaseValue": 53}, {"CaseName": "HeroQualityUp", "CaseValue": 54}, {"CaseName": "SevenDayTasksScore", "CaseValue": 55}]}, {"EnumName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cases": [{"CaseName": "Opposite", "CaseValue": 1}, {"CaseName": "OppositeSideHeroDefense", "CaseValue": 2}, {"CaseName": "OppositeSideHeroRanged", "CaseValue": 3}, {"CaseName": "OppositeSideHeroSupport", "CaseValue": 4}, {"CaseName": "OppoSideHeroFront", "CaseValue": 5}, {"CaseName": "OppoSideHeroBehind", "CaseValue": 6}, {"CaseName": "OppositeSideHeroHpLowest", "CaseValue": 7}, {"CaseName": "PreSkillEffectTarget", "CaseValue": 8}, {"CaseName": "PreSkillEffectTargetElseBoss", "CaseValue": 9}, {"CaseName": "Boss", "CaseValue": 10}, {"CaseName": "OwnForward", "CaseValue": 11}]}, {"EnumName": "LevelType", "Cases": [{"CaseName": "TowerDefense", "CaseValue": 1}, {"CaseName": "ParkOur", "CaseValue": 2}]}, {"EnumName": "LogicType", "Cases": [{"CaseName": "And", "CaseValue": 1}, {"CaseName": "Or", "CaseValue": 2}, {"CaseName": "Invert", "CaseValue": 3}]}, {"EnumName": "LordEquipGradeType", "Cases": [{"CaseName": "LordEquipGrade1", "CaseValue": 1}, {"CaseName": "LordEquipGrade2", "CaseValue": 2}, {"CaseName": "LordEquipGrade3", "CaseValue": 3}, {"CaseName": "LordEquipGrade4", "CaseValue": 4}, {"CaseName": "LordEquipGrade5", "CaseValue": 5}, {"CaseName": "LordEquipGrade6", "CaseValue": 6}, {"CaseName": "LordEquipGrade7", "CaseValue": 7}]}, {"EnumName": "LordEquipType", "Cases": [{"CaseName": "LordEquipType1", "CaseValue": 1}, {"CaseName": "LordEquipType2", "CaseValue": 2}, {"CaseName": "LordEquipType3", "CaseValue": 3}, {"CaseName": "LordEquipType4", "CaseValue": 4}, {"CaseName": "LordEquipType5", "CaseValue": 5}, {"CaseName": "LordEquipType6", "CaseValue": 6}]}, {"EnumName": "MapEventType", "Cases": [{"CaseName": "Monster", "CaseValue": 1}, {"CaseName": "Prop", "CaseValue": 2}, {"CaseName": "Buff", "CaseValue": 3}, {"CaseName": "Obstacle", "CaseValue": 4}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 5}]}, {"EnumName": "MissileTarget", "Cases": [{"CaseName": "Opposite", "CaseValue": 1}, {"CaseName": "OppositeSideHeroDefense", "CaseValue": 2}, {"CaseName": "OppositeSideHeroRanged", "CaseValue": 3}, {"CaseName": "OppositeSideHeroSupport", "CaseValue": 4}, {"CaseName": "OppoSideHeroFront", "CaseValue": 5}, {"CaseName": "OppoSideHeroBehind", "CaseValue": 6}, {"CaseName": "OppositeSideHeroHpLowest", "CaseValue": 7}, {"CaseName": "PreSkillEffectTarget", "CaseValue": 8}, {"CaseName": "PreSkillEffectTargetElseBoss", "CaseValue": 9}, {"CaseName": "Boss", "CaseValue": 10}]}, {"EnumName": "MonsterCareerType", "Cases": [{"CaseName": "<PERSON><PERSON>", "CaseValue": 1}, {"CaseName": "<PERSON>", "CaseValue": 2}, {"CaseName": "Tank", "CaseValue": 3}, {"CaseName": "Assassin", "CaseValue": 4}, {"CaseName": "AirMelee", "CaseValue": 5}, {"CaseName": "AirRanger", "CaseValue": 6}, {"CaseName": "Suicide", "CaseValue": 7}, {"CaseName": "<PERSON><PERSON><PERSON>", "CaseValue": 8}]}, {"EnumName": "MonsterGrade", "Cases": [{"CaseName": "Common", "CaseValue": 1}, {"CaseName": "Elite", "CaseValue": 2}, {"CaseName": "Boss", "CaseValue": 3}]}, {"EnumName": "MonsterPosType", "Cases": [{"CaseName": "Ground", "CaseValue": 1}, {"CaseName": "Air", "CaseValue": 2}]}, {"EnumName": "MonsterRefreshType", "Cases": [{"CaseName": "InitialRefresh", "CaseValue": 1}, {"CaseName": "DelayRefresh", "CaseValue": 2}, {"CaseName": "UpstreamDeathCntRefresh", "CaseValue": 3}, {"CaseName": "PassPlotsRefresh", "CaseValue": 4}, {"CaseName": "AfterRefreshing", "CaseValue": 5}, {"CaseName": "AfterTheGameStarts", "CaseValue": 6}]}, {"EnumName": "PurchaseLimitType", "Cases": [{"CaseName": "DailyLimit", "CaseValue": 1}, {"CaseName": "WeeklyLimit", "CaseValue": 2}, {"CaseName": "MonthlyLimit", "CaseValue": 3}, {"CaseName": "LifeLimit", "CaseValue": 4}, {"CaseName": "UnLimit", "CaseValue": 5}]}, {"EnumName": "RougeTabType", "Cases": [{"CaseName": "EffectTab", "CaseValue": 1}, {"CaseName": "UnlockTab", "CaseValue": 2}, {"CaseName": "ConfigTab", "CaseValue": 3}]}, {"EnumName": "ShopType", "Cases": [{"CaseName": "GuildShop", "CaseValue": 1}, {"CaseName": "ArenaShop", "CaseValue": 2}]}, {"EnumName": "SkillAttrOverlyingType", "Cases": [{"CaseName": "AddOverlying", "CaseValue": 1}, {"CaseName": "MulOverlying", "CaseValue": 2}, {"CaseName": "EnumOverlying", "CaseValue": 3}, {"CaseName": "NoOverlying", "CaseValue": 4}]}, {"EnumName": "SkillDmgType", "Cases": [{"CaseName": "Electrical", "CaseValue": 1}, {"CaseName": "Wind", "CaseValue": 2}, {"CaseName": "Light", "CaseValue": 3}, {"CaseName": "Fire", "CaseValue": 4}, {"CaseName": "Ice", "CaseValue": 5}, {"CaseName": "Physical", "CaseValue": 6}]}, {"EnumName": "SkillType", "Cases": [{"CaseName": "ActiveSkill", "CaseValue": 1}, {"CaseName": "PassiveSkill", "CaseValue": 2}, {"CaseName": "AuraSkill", "CaseValue": 3}]}, {"EnumName": "TaskCounterType", "Cases": [{"CaseName": "Reset", "CaseValue": 1}, {"CaseName": "Total", "CaseValue": 2}]}, {"EnumName": "TaskType", "Cases": [{"CaseName": "<PERSON><PERSON>", "CaseValue": 1}, {"CaseName": "TotalLogin", "CaseValue": 2}, {"CaseName": "LevelBegin", "CaseValue": 3}, {"CaseName": "LevelPass", "CaseValue": 4}, {"CaseName": "LevelPassTo", "CaseValue": 5}, {"CaseName": "ItemBurn", "CaseValue": 6}, {"CaseName": "TotalItemBurn", "CaseValue": 7}, {"CaseName": "HeroLevelUp", "CaseValue": 8}, {"CaseName": "HeroLevelUpTo", "CaseValue": 9}, {"CaseName": "HeroStarUp", "CaseValue": 10}, {"CaseName": "HeroStarUpTo", "CaseValue": 11}, {"CaseName": "HeroSkillUp", "CaseValue": 12}, {"CaseName": "HeroSkillUpTo", "CaseValue": 13}, {"CaseName": "HeroGeneUp", "CaseValue": 14}, {"CaseName": "HeroGeneUpTo", "CaseValue": 15}, {"CaseName": "KillMonster", "CaseValue": 16}, {"CaseName": "TotalKillMonster", "CaseValue": 17}, {"CaseName": "claim_idle_reward", "CaseValue": 18}, {"CaseName": "claim_pass_level_reward", "CaseValue": 19}, {"CaseName": "Cha<PERSON>", "CaseValue": 20}, {"CaseName": "<PERSON>gger", "CaseValue": 21}, {"CaseName": "<PERSON><PERSON>", "CaseValue": 22}, {"CaseName": "Avatar", "CaseValue": 23}, {"CaseName": "JoinGuild", "CaseValue": 24}, {"CaseName": "Sweep", "CaseValue": 25}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON>", "CaseValue": 26}, {"CaseName": "HeroConfig", "CaseValue": 27}, {"CaseName": "LordEquipLvlUp", "CaseValue": 28}, {"CaseName": "LordEquipLvlUpTo", "CaseValue": 29}, {"CaseName": "GemCraft", "CaseValue": 30}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CaseValue": 31}, {"CaseName": "Shopping", "CaseValue": 32}, {"CaseName": "DungeonChallenge", "CaseValue": 33}, {"CaseName": "DungeonSweep", "CaseValue": 34}, {"CaseName": "ArenaChallenge", "CaseValue": 35}, {"CaseName": "TotalActivateHero", "CaseValue": 36}]}, {"EnumName": "TriggerPackType", "Cases": [{"CaseName": "LevelPass", "CaseValue": 1}, {"CaseName": "GemDraw", "CaseValue": 2}, {"CaseName": "<PERSON><PERSON><PERSON><PERSON>", "CaseValue": 3}]}]}