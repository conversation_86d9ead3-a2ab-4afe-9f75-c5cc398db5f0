package util

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/g"
	"bytes"
	"encoding/json"
	"fmt"
	"math/rand"
	"net"
	"net/http"
	"strconv"
	"time"
)

// DaysBetween 计算两个时间之间相隔的自然天数（基于UTC时区）
func DaysBetween(t1, t2 time.Time) int32 {
	// 确保计算基于UTC时区
	utc1 := t1.UTC()
	utc2 := t2.UTC()

	// 将时间调整为当天的起始时间（00:00 UTC）
	y1, m1, d1 := utc1.Date()
	start := time.Date(y1, m1, d1, 0, 0, 0, 0, time.UTC)

	y2, m2, d2 := utc2.Date()
	end := time.Date(y2, m2, d2, 0, 0, 0, 0, time.UTC)

	// 计算时间差（绝对值）
	diff := end.Sub(start)
	if diff < 0 {
		diff = -diff
	}

	// 转换为天数
	return int32(diff.Hours() / 24)
}

func IsSameDay(t1 time.Time, t2 time.Time) bool {
	return t1.Year() == t2.Year() && t1.Month() == t2.Month() && t1.Day() == t2.Day()
}

func IsSameWeek(t1 time.Time, t2 time.Time) bool {
	y1, w1 := t1.ISOWeek()
	y2, w2 := t2.ISOWeek()
	return y1 == y2 && w1 == w2
}

func IsSameMonth(t1 time.Time, t2 time.Time) bool {
	return t1.Year() == t2.Year() && t1.Month() == t2.Month()
}

func CombineRewards(reward1 map[int32]int64, reward2 map[int32]int64) map[int32]int64 {
	for k, v := range reward1 {
		reward2[k] = reward2[k] + v
	}
	return reward2
}

func RandomByWeight(weights map[int32]int32) int32 {
	if weights == nil {
		return 0
	}
	sum := int32(0)
	for _, weight := range weights {
		sum += weight
	}
	if sum == 0 {
		return 0
	}
	randomNumber := rand.Intn(int(sum))
	for id, weight := range weights {
		randomNumber -= int(weight)
		if randomNumber <= 0 {
			return id
		}
	}
	return 0
}

func MinInt32(a int32, b int32) int32 {
	if a < b {
		return a
	}
	return b
}

func MinInt64(a int64, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

func MaxInt32(a int32, b int32) int32 {
	if a > b {
		return a
	}
	return b
}

func MaxInt64(a int64, b int64) int64 {
	if a > b {
		return a
	}
	return b
}

func MinInt(a int, b int) int {
	if a < b {
		return a
	}
	return b
}

func MaxInt(a int, b int) int {
	if a > b {
		return a
	}
	return b
}

func MaxFloat32(a float32, b float32) float32 {
	if a > b {
		return a
	}
	return b
}

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func GenerateSequenceString() string {
	length := 16
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}

	var result []byte
	for _, v := range b {
		result = append(result, charset[v%byte(len(charset))])
	}

	return string(result)
}

func ChangeRewardsToJson(rewards map[int32]int64) string {
	stringKeyMap := make(map[string]int64, len(rewards))
	for key, value := range rewards {
		stringKey := strconv.FormatInt(int64(key), 10) // 将int32转换为字符串
		stringKeyMap[stringKey] = value
	}

	// 将新map转换为JSON
	jsonData, err := json.Marshal(stringKeyMap)
	if err != nil {
		return ""
	}

	return string(jsonData)
}
func CopyMap(src map[int32]int32) map[int32]int32 {
	dst := make(map[int32]int32, len(src))
	for k, v := range src {
		dst[k] = v
	}
	return dst
}

func GetIpCountry(ip string) string {
	// 使用单例模式加载GeoIP数据库

	geoDB := g.GeoDB
	// 如果数据库加载失败，返回未知
	if geoDB == nil {
		return "unknown"
	}

	// 解析IP地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return "invalid_ip"
	}

	// 查询国家信息
	record, err := geoDB.Country(parsedIP)
	if err != nil {
		return "unknown"
	}

	return record.Country.IsoCode
}

// 极简文本消息结构
type FeishuTextMessage struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

// 发送文本消息到飞书 (简化版)
func SendFeishuText(webhookURL string, textContent string) error {
	// 创建消息结构
	message := FeishuTextMessage{
		MsgType: "text",
	}
	message.Content.Text = textContent

	// 转换为JSON
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}

	// 发送HTTP请求
	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("飞书API返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var response struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务错误码
	if response.Code != 0 {
		return fmt.Errorf("飞书API错误: %s (代码 %d)", response.Msg, response.Code)
	}

	return nil
}
