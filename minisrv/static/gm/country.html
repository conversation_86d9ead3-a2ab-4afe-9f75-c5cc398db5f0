<!DOCTYPE html>
<html>
<head>
    <title>Country Restrictions - GM Panel</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
            overflow-y: auto;
        }

        .panel {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .logout-btn {
            padding: 8px 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background-color: #c0392b;
        }

        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"], select {
            padding: 8px;
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .checkbox-container {
            margin-bottom: 15px;
        }

        .country-list {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .country-item {
            width: 200px;
            margin-bottom: 10px;
        }

        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background-color: #2980b9;
        }

        button.secondary {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        button.secondary:hover {
            background-color: #e0e0e0;
        }

        .test-container {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }

        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }

        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }

        .error {
            background-color: #f2dede;
            color: #a94442;
        }

        .whitelist-container {
            margin-top: 20px;
        }

        .whitelist-item {
            display: flex;
            margin-bottom: 10px;
        }

        .whitelist-item input {
            flex-grow: 1;
            margin-right: 10px;
        }

        .whitelist-item button {
            padding: 5px 10px;
        }

        .add-whitelist {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
            <div class="menu-item active">国家限制</div>
            <div class="menu-item" onclick="window.location.href='/gm/users'">用户管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
            <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <h1>国家登录限制</h1>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>

        <div class="content">
            <div class="panel">
                <div class="form-group">
                    <div class="checkbox-container">
                        <input type="checkbox" id="enableRestrictions" onchange="toggleRestrictions()">
                        <label for="enableRestrictions" style="display: inline;">启用国家限制</label>
                    </div>
                    <p>启用后，只有来自选定国家的玩家才能登录游戏。</p>
                </div>
                
                <div id="restrictionsContainer" style="display: none;">


                    <div class="form-group">
                        <label>允许的国家:</label>
                        <div class="country-list" id="countryList">
                            <!-- Country checkboxes will be populated here -->
                        </div>
                    </div>

                    <div class="form-group whitelist-container">
                        <label>IP白名单 (这些IP将绕过限制):</label>
                        <div id="whitelistContainer">
                            <!-- Whitelist items will be added here -->
                        </div>
                        <div class="add-whitelist">
                            <button onclick="addWhitelistField()">添加IP到白名单</button>
                        </div>
                    </div>

                </div>
                <div class="form-group">
                    <button onclick="saveRestrictions()">保存设置</button>
                    <button class="secondary" onclick="resetForm()">重置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Countries list - common countries first, then alphabetical
        const countries = [
            { code: 'US', name: '美国' },
            { code: 'CN', name: '中国' },
            { code: 'JP', name: '日本' },
            { code: 'KR', name: '韩国' },
            { code: 'GB', name: '英国' },
            { code: 'CA', name: '加拿大' },
            { code: 'AU', name: '澳大利亚' },
            { code: 'DE', name: '德国' },
            { code: 'FR', name: '法国' },
            { code: 'IT', name: '意大利' },
            { code: 'ES', name: '西班牙' },
            { code: 'BR', name: '巴西' },
            { code: 'RU', name: '俄罗斯' },
            { code: 'IN', name: '印度' },
            { code: 'MX', name: '墨西哥' },
            { code: 'ID', name: '印度尼西亚' },
            { code: 'PH', name: '菲律宾' },
            { code: 'VN', name: '越南' },
            { code: 'TH', name: '泰国' },
            { code: 'MY', name: '马来西亚' },
            { code: 'SG', name: '新加坡' },
            { code: 'TR', name: '土耳其' },
            { code: 'SA', name: '沙特阿拉伯' },
            { code: 'AE', name: '阿联酋' },
            // 补充完整列表
            { code: 'AR', name: '阿根廷' },
            { code: 'NL', name: '荷兰' },
            { code: 'SE', name: '瑞典' },
            { code: 'CH', name: '瑞士' },
            { code: 'BE', name: '比利时' },
            { code: 'PL', name: '波兰' },
            { code: 'NO', name: '挪威' },
            { code: 'AT', name: '奥地利' },
            { code: 'DK', name: '丹麦' },
            { code: 'FI', name: '芬兰' },
            { code: 'ZA', name: '南非' },
            { code: 'EG', name: '埃及' },
            { code: 'NG', name: '尼日利亚' },
            { code: 'KE', name: '肯尼亚' },
            { code: 'CL', name: '智利' },
            { code: 'CO', name: '哥伦比亚' },
            { code: 'PE', name: '秘鲁' },
            { code: 'VE', name: '委内瑞拉' },
            { code: 'PK', name: '巴基斯坦' },
            { code: 'BD', name: '孟加拉国' },
            { code: 'IR', name: '伊朗' },
            { code: 'IQ', name: '伊拉克' },
            { code: 'IL', name: '以色列' },
            { code: 'KW', name: '科威特' },
            { code: 'QA', name: '卡塔尔' },
            { code: 'OM', name: '阿曼' },
            { code: 'KZ', name: '哈萨克斯坦' },
            { code: 'UZ', name: '乌兹别克斯坦' },
            { code: 'HK', name: '中国香港' },
            { code: 'TW', name: '中国台湾' },
            { code: 'MO', name: '中国澳门' },
            { code: 'PT', name: '葡萄牙' },
            { code: 'GR', name: '希腊' },
            { code: 'IE', name: '爱尔兰' },
            { code: 'CZ', name: '捷克' },
            { code: 'HU', name: '匈牙利' },
            { code: 'RO', name: '罗马尼亚' },
            { code: 'UA', name: '乌克兰' },
            { code: 'BY', name: '白俄罗斯' },
            { code: 'SK', name: '斯洛伐克' },
            { code: 'BG', name: '保加利亚' },
            { code: 'RS', name: '塞尔维亚' },
            { code: 'HR', name: '克罗地亚' },
            { code: 'SI', name: '斯洛文尼亚' },
            { code: 'EE', name: '爱沙尼亚' },
            { code: 'LT', name: '立陶宛' },
            { code: 'LV', name: '拉脱维亚' },
            { code: 'IS', name: '冰岛' },
            { code: 'LU', name: '卢森堡' },
            { code: 'CY', name: '塞浦路斯' },
            { code: 'MT', name: '马耳他' },
            { code: 'NZ', name: '新西兰' },
            { code: 'CR', name: '哥斯达黎加' },
            { code: 'PA', name: '巴拿马' },
            { code: 'DO', name: '多米尼加' },
            { code: 'EC', name: '厄瓜多尔' },
            { code: 'GT', name: '危地马拉' },
            { code: 'CU', name: '古巴' },
            { code: 'JM', name: '牙买加' },
            { code: 'BO', name: '玻利维亚' },
            { code: 'PY', name: '巴拉圭' },
            { code: 'UY', name: '乌拉圭' },
            { code: 'DZ', name: '阿尔及利亚' },
            { code: 'MA', name: '摩洛哥' },
            { code: 'TN', name: '突尼斯' },
            { code: 'GH', name: '加纳' },
            { code: 'ET', name: '埃塞俄比亚' },
            { code: 'TZ', name: '坦桑尼亚' },
            { code: 'UG', name: '乌干达' },
            { code: 'CM', name: '喀麦隆' },
            { code: 'SD', name: '苏丹' },
            { code: 'MM', name: '缅甸' },
            { code: 'LK', name: '斯里兰卡' },
            { code: 'NP', name: '尼泊尔' },
            { code: 'KH', name: '柬埔寨' },
            { code: 'LA', name: '老挝' },
            { code: 'MN', name: '蒙古' },
            { code: 'BN', name: '文莱' },
            { code: 'FJ', name: '斐济' },
            { code: 'PG', name: '巴布亚新几内亚' }
        ];


        let currentConfig = {
            enabled: false,
            channels: [],
            languages: [],
            allowedCountries: [],
            whitelistIPs: []
        };
        
        // Check authentication on page load
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }
            
            // Verify token
            fetch('/gm/api/verify-token', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Token invalid');
                }
                // Load current restrictions
                loadRestrictions();
                // Populate countries list
                populateCountries();
            })
            .catch(error => {
                console.error('Token validation failed:', error);
                localStorage.removeItem('gmToken');
                window.location.href = '/gm/login';
            });
        };
        
        function populateCountries() {
            const countryList = document.getElementById('countryList');
            countryList.innerHTML = ''; // Clear existing items
            
            // Add countries
            countries.forEach(country => {
                const div = document.createElement('div');
                div.className = 'country-item';
                div.innerHTML = `
                    <input type="checkbox" id="country-${country.code}" value="${country.code}">
                    <label for="country-${country.code}">${country.name} (${country.code})</label>
                `;
                countryList.appendChild(div);
            });
        }
        
        function loadRestrictions() {
            const token = localStorage.getItem('gmToken');
            
            fetch('/gm/api/country-restrictions', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => response.json())
            .then(data => {
                currentConfig = data;
                updateFormFromConfig();
            })
            .catch(error => {
                console.error('Error loading restrictions:', error);
                alert('加载当前限制设置失败。');
            });
        }
        
        function updateFormFromConfig() {
            // Set master switch
            document.getElementById('enableRestrictions').checked = currentConfig.enabled;
            toggleRestrictions();
            
            // Set channels
            document.querySelectorAll('#channelList input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.value === 'all') {
                    // Skip the "All" checkbox
                    return;
                }
                checkbox.checked = currentConfig.channels.includes(checkbox.value);
            });
            
            // Set languages
            document.querySelectorAll('#languageList input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.value === 'all') {
                    // Skip the "All" checkbox
                    return;
                }
                checkbox.checked = currentConfig.languages.includes(checkbox.value);
            });
            
            // Set countries
            document.querySelectorAll('#countryList input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = currentConfig.allowedCountries.includes(checkbox.value);
            });
            
            // Set whitelist IPs
            updateWhitelistUI();
        }
        
        function updateWhitelistUI() {
            const container = document.getElementById('whitelistContainer');
            container.innerHTML = '';
            
            if (currentConfig.whitelistIPs.length === 0) {
                // Add an empty field if no IPs
                addWhitelistField();
                return;
            }
            
            currentConfig.whitelistIPs.forEach((ip, index) => {
                const div = document.createElement('div');
                div.className = 'whitelist-item';
                div.innerHTML = `
                    <input type="text" class="whitelist-ip" value="${ip}">
                    <button onclick="removeWhitelistField(this)">移除</button>
                `;
                container.appendChild(div);
            });
        }
        
        function addWhitelistField() {
            const container = document.getElementById('whitelistContainer');
            const div = document.createElement('div');
            div.className = 'whitelist-item';
            div.innerHTML = `
                <input type="text" class="whitelist-ip" placeholder="输入IP地址">
                <button onclick="removeWhitelistField(this)">移除</button>
            `;
            container.appendChild(div);
        }
        
        function removeWhitelistField(button) {
            const item = button.parentElement;
            item.remove();
        }
        
        function toggleRestrictions() {
            const enabled = document.getElementById('enableRestrictions').checked;
            document.getElementById('restrictionsContainer').style.display = enabled ? 'block' : 'none';
        }
        
        function saveRestrictions() {
            const enabled = document.getElementById('enableRestrictions').checked;
            
            // Get selected channels
            const channels = [];
            document.querySelectorAll('#channelList input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value !== 'all') {
                    channels.push(checkbox.value);
                }
            });
            
            // Get selected languages
            const languages = [];
            document.querySelectorAll('#languageList input[type="checkbox"]:checked').forEach(checkbox => {
                if (checkbox.value !== 'all') {
                    languages.push(checkbox.value);
                }
            });
            
            // Get selected countries
            const allowedCountries = [];
            document.querySelectorAll('#countryList input[type="checkbox"]:checked').forEach(checkbox => {
                allowedCountries.push(checkbox.value);
            });
            
            // Get whitelist IPs
            const whitelistIPs = [];
            document.querySelectorAll('.whitelist-ip').forEach(input => {
                const ip = input.value.trim();
                if (ip) {
                    whitelistIPs.push(ip);
                }
            });
            
            const config = {
                enabled,
                channels,
                languages,
                allowedCountries,
                whitelistIPs
            };
            
            const token = localStorage.getItem('gmToken');
            
            fetch('/gm/api/country-restrictions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': token
                },
                body: JSON.stringify(config)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to save restrictions');
                }
                return response.json();
            })
            .then(data => {
                alert('限制设置保存成功！');
                currentConfig = config;
            })
            .catch(error => {
                console.error('Error saving restrictions:', error);
                alert('保存限制设置失败。');
            });
        }
        
        function resetForm() {
            updateFormFromConfig();
        }
        
        function testIPRestriction() {
            const ip = document.getElementById('testIP').value.trim();
            const channel = document.getElementById('testChannel').value;
            const language = document.getElementById('testLanguage').value;
            
            if (!ip) {
                alert('Please enter an IP address to test.');
                return;
            }
            
            const token = localStorage.getItem('gmToken');
            
            fetch('/gm/api/test-ip-restriction', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': token
                },
                body: JSON.stringify({
                    ip,
                    channel,
                    language
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                resultDiv.style.display = 'block';
                resultDiv.className = data.allowed ? 'result success' : 'result error';
                resultDiv.innerHTML = `
                    <p><strong>IP:</strong> ${data.ip}</p>
                    <p><strong>Country:</strong> ${data.country}</p>
                    <p><strong>Access:</strong> ${data.allowed ? 'Allowed' : 'Blocked'}</p>
                `;
            })
            .catch(error => {
                console.error('Error testing IP:', error);
                alert('Failed to test IP restriction.');
            });
        }
        
        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }
        
        // Handle "All" checkboxes
        document.getElementById('channel-all').addEventListener('change', function() {
            const checked = this.checked;
            document.querySelectorAll('#channelList input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.value !== 'all') {
                    checkbox.checked = checked;
                }
            });
        });
        
        document.getElementById('lang-all').addEventListener('change', function() {
            const checked = this.checked;
            document.querySelectorAll('#languageList input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.value !== 'all') {
                    checkbox.checked = checked;
                }
            });
        });
    </script>
</body>
</html>