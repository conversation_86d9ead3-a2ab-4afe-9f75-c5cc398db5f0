<!DOCTYPE html>
<html>
<head>
    <title>用户管理 - GM管理后台</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar .header {
            padding: 0 20px 20px 20px;
            border-bottom: 1px solid #34495e;
        }

        .sidebar .menu {
            margin-top: 20px;
        }

        .sidebar .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .sidebar .menu-item:hover {
            background-color: #34495e;
        }

        .sidebar .menu-item.active {
            background-color: #3498db;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            background-color: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .panel {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .logout-btn {
            padding: 8px 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background-color: #c0392b;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        .edit-btn {
            background-color: #3498db;
            color: white;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .nav-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            margin-bottom: -1px;
        }

        .nav-tab.active {
            border: 1px solid #ddd;
            border-bottom-color: white;
            background: white;
        }

        .table-actions {
            display: flex;
            gap: 5px;
        }

        .search-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .search-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-btn {
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 用户详情弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 4px;
            width: 80%;
            max-width: 600px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f5f5f5;
        }

        .pagination button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="header">
            <h2>GM管理后台</h2>
        </div>
        <div class="menu">
            <div class="menu-item" onclick="window.location.href='/gm/'">数据概览</div>
            <div class="menu-item" onclick="window.location.href='/gm/country'">国家限制</div>
            <div class="menu-item active">用户管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/items'">道具管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/mail'">邮件系统</div>
            <div class="menu-item" onclick="window.location.href='/gm/announce'">公告管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/activity'">活动管理</div>
            <div class="menu-item" onclick="window.location.href='/gm/logs'">系统日志</div>
        </div>
    </div>

    <div class="main-content">
        <div class="top-bar">
            <h1>用户管理</h1>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>

        <div class="content">
            <div class="panel">
                <div class="search-container">
                    <input type="text" id="userSearchInput" class="search-input" placeholder="输入用户ID或用户名搜索">
                    <button onclick="searchUsers()" class="search-btn">搜索</button>
                    <button onclick="clearSearch()" class="search-btn" style="background-color: #95a5a6;">清除</button>
                </div>
                
                <div class="nav-tabs">
                    <div class="nav-tab active" onclick="switchUserTab('all')">全部用户</div>
                    <div class="nav-tab" onclick="switchUserTab('online')">在线用户</div>
                    <div class="nav-tab" onclick="switchUserTab('banned')">封禁用户</div>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>等级</th>
                            <th>最后登录</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- 用户数据将在这里动态加载 -->
                    </tbody>
                </table>

                <!-- 分页控件 -->
                <div class="pagination">
                    <button id="prevPage" onclick="changePage(-1)">上一页</button>
                    <span id="pageInfo">第 1 页，共 1 页</span>
                    <button id="nextPage" onclick="changePage(1)">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户详情/编辑弹窗 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">用户详情</h3>
                <span class="close" onclick="closeUserModal()">&times;</span>
            </div>
            <div id="modalBody">
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="text" id="editUserId" readonly>
                </div>
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" id="editUserName">
                </div>
                <div class="form-group">
                    <label>等级:</label>
                    <input type="number" id="editUserLevel">
                </div>
                <div class="form-group">
                    <label>状态:</label>
                    <select id="editUserStatus">
                        <option value="active">正常</option>
                        <option value="banned">封禁</option>
                        <option value="suspended">暂停</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>封禁原因:</label>
                    <textarea id="banReason" rows="3" placeholder="如果封禁用户，请填写原因"></textarea>
                </div>
                <div class="form-group">
                    <button onclick="saveUserChanges()" class="action-btn edit-btn">保存更改</button>
                    <button onclick="closeUserModal()" class="action-btn" style="background-color: #95a5a6; color: white;">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let currentTab = 'all';
        let searchTerm = '';

        // 页面加载时的初始化
        window.onload = function() {
            const token = localStorage.getItem('gmToken');
            if (!token) {
                window.location.href = '/gm/login';
                return;
            }
            
            // 验证token
            fetch('/gm/api/verify-token', {
                headers: {
                    'X-GM-Token': token
                }
            })
            .then(response => {
                if (!response.ok) {
                    localStorage.removeItem('gmToken');
                    window.location.href = '/gm/login';
                    return;
                }
                // Token有效，加载用户数据
                loadUsers();
            })
            .catch(error => {
                console.error('Token验证失败:', error);
                localStorage.removeItem('gmToken');
                window.location.href = '/gm/login';
            });
        }

        // 用户管理相关函数
        function switchUserTab(tab) {
            document.querySelectorAll('.nav-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            currentTab = tab;
            currentPage = 1;
            loadUsers();
        }

        function searchUsers() {
            searchTerm = document.getElementById('userSearchInput').value.trim();
            currentPage = 1;
            loadUsers();
        }

        function clearSearch() {
            document.getElementById('userSearchInput').value = '';
            searchTerm = '';
            currentPage = 1;
            loadUsers();
        }

        function loadUsers() {
            const params = new URLSearchParams({
                page: currentPage,
                pageSize: pageSize,
                tab: currentTab
            });
            
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            fetch('/gm/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify({
                    page: currentPage,
                    pageSize: pageSize,
                    tab: currentTab,
                    search: searchTerm
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('API Response:', data);
                const tbody = document.getElementById('users-table-body');
                tbody.innerHTML = '';
                
                if (data && data.data && data.data.users) {
                    data.data.users.forEach(user => {
                        const tr = document.createElement('tr');
                        const lastLoginDate = user.lastLogin ? new Date(user.lastLogin * 1000).toLocaleString() : 'Never';
                        const status = getStatusText(user.status || 'active');
                        
                        tr.innerHTML = `
                            <td>${user.uid}</td>
                            <td>${user.name}</td>
                            <td>${user.level}</td>
                            <td>${lastLoginDate}</td>
                            <td>${status}</td>
                            <td class="table-actions">
                                <button class="action-btn edit-btn" onclick="editUser(${user.uid})">编辑</button>
                                <button class="action-btn delete-btn" onclick="banUser(${user.uid})">${user.status === 'banned' ? '解封' : '封禁'}</button>
                                <button class="action-btn" style="background-color: #f39c12; color: white;" onclick="viewUserDetails(${user.uid})">详情</button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                    
                    // 更新分页信息
                    updatePagination(data.data);
                } else {
                    const tr = document.createElement('tr');
                    tr.innerHTML = '<td colspan="6" style="text-align: center;">没有找到用户数据</td>';
                    tbody.appendChild(tr);
                }
            })
            .catch(error => {
                console.error('Error loading users:', error);
                const tbody = document.getElementById('users-table-body');
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: red;">加载用户数据失败</td></tr>';
            });
        }

        function getStatusText(status) {
            const statusMap = {
                'active': '正常',
                'banned': '封禁',
                'suspended': '暂停',
                'online': '在线'
            };
            return statusMap[status] || '正常';
        }

        function updatePagination(data) {
            const total = data.total || 0;
            totalPages = Math.ceil(total / pageSize);
            
            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${total} 个用户)`;
            document.getElementById('prevPage').disabled = currentPage <= 1;
            document.getElementById('nextPage').disabled = currentPage >= totalPages;
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadUsers();
            }
        }

        function editUser(userId) {
            // 获取用户详情并打开编辑弹窗
            fetch(`/gm/api/users/${userId}`, {
                headers: {
                    'X-GM-Token': localStorage.getItem('gmToken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.data) {
                    const user = data.data;
                    document.getElementById('modalTitle').textContent = '编辑用户';
                    document.getElementById('editUserId').value = user.uid;
                    document.getElementById('editUserName').value = user.name;
                    document.getElementById('editUserLevel').value = user.level;
                    document.getElementById('editUserStatus').value = user.status || 'active';
                    document.getElementById('banReason').value = user.banReason || '';
                    document.getElementById('userModal').style.display = 'block';
                } else {
                    alert('获取用户信息失败');
                }
            })
            .catch(error => {
                console.error('Error fetching user details:', error);
                // 如果API不存在，使用模拟数据
                document.getElementById('modalTitle').textContent = '编辑用户';
                document.getElementById('editUserId').value = userId;
                document.getElementById('editUserName').value = 'User' + userId;
                document.getElementById('editUserLevel').value = 1;
                document.getElementById('editUserStatus').value = 'active';
                document.getElementById('banReason').value = '';
                document.getElementById('userModal').style.display = 'block';
            });
        }

        function viewUserDetails(userId) {
            // 查看用户详情（只读模式）
            editUser(userId);
            document.getElementById('modalTitle').textContent = '用户详情';
            // 设置所有输入框为只读
            document.querySelectorAll('#modalBody input, #modalBody select, #modalBody textarea').forEach(input => {
                input.disabled = true;
            });
        }

        function banUser(userId) {
            const action = event.target.textContent === '解封' ? 'unban' : 'ban';
            const message = action === 'ban' ? '确定要封禁这个用户吗？' : '确定要解封这个用户吗？';
            
            if (confirm(message)) {
                fetch('/gm/api/users/ban', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-GM-Token': localStorage.getItem('gmToken')
                    },
                    body: JSON.stringify({
                        userId: userId,
                        action: action,
                        reason: action === 'ban' ? prompt('请输入封禁原因:') : ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data && data.code === 200) {
                        alert(action === 'ban' ? '用户已封禁' : '用户已解封');
                        loadUsers();
                    } else {
                        alert('操作失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('Error banning/unbanning user:', error);
                    alert('操作失败，请检查网络连接');
                });
            }
        }

        function saveUserChanges() {
            const userId = document.getElementById('editUserId').value;
            const userData = {
                userId: parseInt(userId),
                name: document.getElementById('editUserName').value,
                level: parseInt(document.getElementById('editUserLevel').value),
                status: document.getElementById('editUserStatus').value,
                banReason: document.getElementById('banReason').value
            };

            fetch('/gm/api/users/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-GM-Token': localStorage.getItem('gmToken')
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.code === 200) {
                    alert('用户信息已更新');
                    closeUserModal();
                    loadUsers();
                } else {
                    alert('更新失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error updating user:', error);
                alert('更新失败，请检查网络连接');
            });
        }

        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
            // 重新启用所有输入框
            document.querySelectorAll('#modalBody input, #modalBody select, #modalBody textarea').forEach(input => {
                input.disabled = false;
            });
        }

        function logout() {
            localStorage.removeItem('gmToken');
            window.location.href = '/gm/login';
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeUserModal();
            }
        }
    </script>
</body>
</html>
