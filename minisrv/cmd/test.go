package cmd

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/orm"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/tasklet"
)

func Test(ctx context.Context) {
	return
	tasklet.Invoke(ctx, "TestLock", func(ctx context.Context) {
		uid := int64(922712)
		gems, _ := orm.GetAll[*model.LordGem](ctx, uid)
		for _, gem := range gems {
			gem.SetBeUsed(ctx, 0)
		}

	})

}
