package iap

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"net/http"
	"strconv"
)

func SubmitGrowthFund(ctx context.Context, uid int64, packageId int32, orderId string, productId string, seq int32, w http.ResponseWriter) error {
	fundLine := cfg_mgr.Cfg.IapLevelFundTable.FilterSlice(func(v *servercfg.IapLevelFundTableCfg) bool {
		return v.IapPackageId == packageId
	})
	if fundLine == nil || len(fundLine) == 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("fund is not exist")
	}
	fundId := fundLine[0].Id
	growthFund, err := model.GetGrowthFundModel(ctx, uid, fundId)
	if err != nil {
		return err
	}
	if growthFund == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("fund is not exist")
	}
	growthFund.SetBuyTimes(ctx, growthFund.BuyTimes()+1)
	return nil
}

func GetGrowthFundReward(ctx context.Context, uid int64, fundId int32) ([]*wrpc.Rewards, error) {
	stageModel, _ := model.GetMainLineStage(ctx, uid)
	lines := cfg_mgr.Cfg.IapLevelFundRewardTable.FilterSlice(func(v *servercfg.IapLevelFundRewardTableCfg) bool {
		return v.Level <= stageModel.StageId() && v.Fund == fundId
	})
	retReward := map[int32]int32{}
	fund, err := model.GetGrowthFundModel(ctx, uid, fundId)
	if err != nil {
		return nil, err
	}
	normalRewardStatus := fund.NormalRewardStatus()
	vipRewardStatus := fund.VipRewardStatus()
	for _, v := range lines {

		if fund.BuyTimes() > 0 {
			if vipRewardStatus[v.Id] == 0 {
				vipRewardStatus[v.Id] = 1
				for _, reward := range v.RewardVip {
					retReward[reward.RewardType] += reward.RewardValue
				}
			}
		}
		if normalRewardStatus[v.Id] == 0 {
			normalRewardStatus[v.Id] = 1
			retReward[v.RewardFree.RewardType] += v.RewardFree.RewardValue
		}
	}
	ret := []*wrpc.Rewards{}
	if retReward != nil {
		seq := util.GenerateSequenceString()
		for k, v := range retReward {
			model.AddItem(ctx, uid, k, int64(v), bi.ItemFlowReason_Growth_Fund, strconv.Itoa(int(fundId)), seq)
			ret = append(ret, &wrpc.Rewards{ItemId: k, ItemValue: int64(v)})
		}
	}

	return ret, nil
}
