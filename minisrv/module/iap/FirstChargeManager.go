package iap

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/cfg_mgr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/cfg/servercfg"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/sdk/bi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/wire/wrpc"
	"context"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/timestamp"
	"net/http"
	"strconv"
	"time"
)

func SubmitFirstCharge(ctx context.Context, uid int64, packageId int32, orderId string, productId string, seq int32, w http.ResponseWriter) error {
	chargeLine := cfg_mgr.Cfg.Iap1stTable.FilterSlice(func(v *servercfg.Iap1stTableCfg) bool {
		return v.IapPackageId == packageId
	})
	if chargeLine == nil || len(chargeLine) == 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("charge  is not exist")
	}
	chargeId := chargeLine[0].Id
	firstCharge, err := model.GetFirstChargeModel(ctx, uid, chargeId)
	if err != nil {
		return err
	}
	if firstCharge == nil {
		return kdmerr.SysInvalidArguments.CastErrorf("fund is not exist")
	}
	if firstCharge.FirstChargeTime() != 0 {
		return kdmerr.SysInvalidArguments.CastErrorf("already charge")
	}
	firstCharge.SetFirstChargeTime(ctx, timestamp.UTCSeconds(time.Now().Unix()))
	collectStatus := firstCharge.CollectStatus()
	collectStatus[1] = true
	firstCharge.SetCollectStatus(ctx, collectStatus)
	return nil
}

func GetFirstChargeReward(ctx context.Context, uid int64, chargeId int32, day int32) ([]*wrpc.Rewards, error) {
	firstCharge, err := model.GetFirstChargeModel(ctx, uid, chargeId)
	if err != nil {
		return nil, err
	}
	if firstCharge == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("fund is not exist")
	}
	if firstCharge.FirstChargeTime() == 0 {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("not charge")
	}
	chargeLine := cfg_mgr.Cfg.Iap1stTable.Get(chargeId)
	if chargeLine == nil {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("charge is not exist")
	}

	collectStatus := firstCharge.CollectStatus()
	if collectStatus[day] == true {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("already collect")
	}
	//检查当前时间和充值时间隔了几个自然天
	if util.DaysBetween(firstCharge.FirstChargeTime().Time(), time.Now())+1 < day {
		return nil, kdmerr.SysInvalidArguments.CastErrorf("time is not right")
	}

	collectStatus[day] = true
	firstCharge.SetCollectStatus(ctx, collectStatus)
	rewards := []*wrpc.Rewards{}
	sequence := util.GenerateSequenceString()
	var reward []*servercfg.RewardKVS
	if day == 2 {
		reward = chargeLine.D2
	} else if day == 3 {
		reward = chargeLine.D3
	}
	for _, v := range reward {
		model.AddItem(ctx, uid, v.RewardType, int64(v.RewardValue), bi.ItemFlowReason_First_Charge, strconv.Itoa(int(chargeId)), sequence)
		rewards = append(rewards, &wrpc.Rewards{ItemId: v.RewardType, ItemValue: int64(v.RewardValue)})
	}
	return rewards, nil

}
