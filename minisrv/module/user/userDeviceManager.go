package user

import (
	"bitbucket.org/kingsgroup/gog-knights/minisrv/handler/gmApi"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/kdmerr"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/logger"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/model"
	"bitbucket.org/kingsgroup/gog-knights/minisrv/util"
	"context"
	"encoding/json"
	"gitlab-ee.funplus.io/backend-platform/zplus-go/logging"
	"net"
	"strings"
)

/**
{\"data_version\":\"1.0\",\"launch_id\":\"launch_a32768ae-8c52-4e49-b941-4682a0b479c7\",\"fpid\":\"fp_account_global:*********\",\"log_source\":\"sdk\",\"session_id\":\"session_1ba6f868-104e-444b-bb82-522d61e9bf05\",\"app_id\":\"gogx.global.dev\",\"sub_version\":\"1.0.1\",\"properties\":{\"device_id\":\"7e290592c73d41e7\",\"os\":\"android\",\"app_version\":\"1.0.0\",\"package_label\":\"\",\"ip\":\"**************\",\"device_level\":1,\"game_uid\":\"\",\"pkg_channel\":\"google.official.Adyeqn\",\"bundle_id\":\"com.puzza.x\",\"fp_device_id\":\"20241012174945f9ccb427fefda885a435d4eac73dd9e2f63cae614d7ee6b0\",\"gameserver_id\":\"\",\"appsflyer_id\":\"*************-7947989260870833298\",\"sub_channel_id\":\"google.official\",\"lang\":\"zh\",\"channel_id\":\"google\",\"sub_channel_code\":\"google.official.Adyeqn.********\"}}"}
*/

type DeviceInfo struct {
	DataVersion string          `json:"data_version"`
	LaunchID    string          `json:"launch_id"`
	Fpid        string          `json:"fpid"`
	LogSource   string          `json:"log_source"`
	SessionID   string          `json:"session_id"`
	AppID       string          `json:"app_id"`
	SubVersion  string          `json:"sub_version"`
	Properties  EventProperties `json:"properties"`
}
type EventProperties struct {
	DeviceID       string `json:"device_id"`
	Os             string `json:"os"`
	AppVersion     string `json:"app_version"`
	PackageLabel   string `json:"package_label"`
	Ip             string `json:"ip"`
	DeviceLevel    int    `json:"device_level"`
	GameUID        string `json:"game_uid"`
	PkgChannel     string `json:"pkg_channel"`
	BundleID       string `json:"bundle_id"`
	FpDeviceID     string `json:"fp_device_id"`
	GameserverID   string `json:"gameserver_id"`
	AppsflyerID    string `json:"appsflyer_id"`
	SubChannelID   string `json:"sub_channel_id"`
	Lang           string `json:"lang"`
	ChannelID      string `json:"channel_id"`
	SubChannelCode string `json:"sub_channel_code"`
}

// 检查 ip 地址
func CheckIP(ctx context.Context, ip string) error {
	logging.Infof(ctx, "check ip : %s", ip)
	config, err := gmApi.LoadRestrictionConfig()
	if err != nil {
		return err
	}
	if !config.Enabled {
		return nil
	}
	// 检查IP是否在白名单中
	for _, pattern := range config.WhitelistIPs {
		// 1. CIDR格式匹配 (e.g., ***********/24)
		if strings.Contains(pattern, "/") {
			_, cidrNet, err := net.ParseCIDR(pattern)
			if err == nil {
				ipAddr := net.ParseIP(ip)
				if ipAddr != nil && cidrNet.Contains(ipAddr) {
					return nil // 匹配CIDR规则
				}
			}
			continue
		}

		// 2. 通配符格式匹配 (e.g., 192.168.*)
		if strings.Contains(pattern, "*") {
			patternSegments := strings.Split(pattern, ".")
			ipSegments := strings.Split(ip, ".")
			if len(patternSegments) != 4 || len(ipSegments) != 4 {
				continue // 忽略无效分段
			}

			match := true
			for i := 0; i < 4; i++ {
				if patternSegments[i] == "*" {
					continue // 通配符跳过检查
				}
				if patternSegments[i] != ipSegments[i] {
					match = false
					break
				}
			}
			if match {
				return nil // 匹配通配符规则
			}
			continue
		}

		// 3. 精确字符串匹配
		if pattern == ip {
			return nil
		}
	}
	//检查所在国家是否允许
	country := util.GetIpCountry(ip)
	for _, allowedCountry := range config.AllowedCountries {
		if allowedCountry == country {
			return nil
		}
	}
	//https://open.feishu.cn/open-apis/bot/v2/hook/fd0ba3bc-d9f9-4ca9-806e-a34667973c04
	util.SendFeishuText("https://open.feishu.cn/open-apis/bot/v2/hook/fd0ba3bc-d9f9-4ca9-806e-a34667973c04", "ip 触发限制: "+ip+" country: "+country)
	return kdmerr.IpIsInLimit.CastErrorf("ip is not allowed")
}

func SetUserDevice(ctx context.Context, uid int64, devices string) error {
	// Empty
	var deviceInfo DeviceInfo
	// 使用json.Unmarshal进行解析
	err := json.Unmarshal([]byte(devices), &deviceInfo)
	if err != nil {
		return err
	}
	deviceModel, isNew, _ := model.GetUserDevice(ctx, uid)

	if deviceModel.Lang() == "" {
		isNew = true
	}
	if deviceInfo.AppID != "" {
		deviceModel.SetAppsflyerId(ctx, deviceInfo.AppID)
	}
	if deviceInfo.Fpid != "" {
		deviceModel.SetFpid(ctx, deviceInfo.Fpid)
	}
	if deviceInfo.Properties.DeviceID != "" {
		deviceModel.SetDeviceId(ctx, deviceInfo.Properties.DeviceID)
	}
	if deviceInfo.DataVersion != "" {
		deviceModel.SetDataVersion(ctx, deviceInfo.DataVersion)
	}
	if deviceInfo.Properties.Ip != "" {
		deviceModel.SetIp(ctx, deviceInfo.Properties.Ip)
	}
	if deviceInfo.Properties.PkgChannel != "" {
		deviceModel.SetPkgChannel(ctx, deviceInfo.Properties.PkgChannel)
	}
	if deviceInfo.Properties.Lang != "" {
		deviceModel.SetLang(ctx, deviceInfo.Properties.Lang)
	}
	if deviceInfo.Properties.AppVersion != "" {
		deviceModel.SetAppVersion(ctx, deviceInfo.Properties.AppVersion)
	}
	if deviceInfo.Properties.Os != "" {
		deviceModel.SetOs(ctx, deviceInfo.Properties.Os)
	}
	if deviceInfo.Properties.DeviceLevel != 0 {
		deviceModel.SetLevel(ctx, int32(deviceInfo.Properties.DeviceLevel))
	}
	if deviceInfo.Properties.AppsflyerID != "" {
		deviceModel.SetAppsflyerId(ctx, deviceInfo.Properties.AppsflyerID)
	}
	if &deviceInfo.Properties.FpDeviceID != nil {
		deviceModel.SetFpDeviceId(ctx, deviceInfo.Properties.FpDeviceID)
	}
	if isNew {
		logger.LogCore(ctx, uid, "new_uid", logger.NewDetail())
	}

	if deviceInfo.Properties.Ip != "" {
		err = CheckIP(ctx, deviceInfo.Properties.Ip)
		if err != nil {
			return err
		}
	}
	return nil
}

//.  /usr/share/GeoIP/GeoLite2-Country.mmdb
