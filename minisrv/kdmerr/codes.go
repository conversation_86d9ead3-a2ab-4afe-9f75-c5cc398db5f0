package kdmerr

import "gitlab-ee.funplus.io/backend-platform/zplus-go/zerror"

const (
	None                                           = zerror.None
	SysInvalidArguments                            = zerror.SysInvalidArguments
	SysUnknown                                     = zerror.SysUnknown
	SysServerUnavailable                           = zerror.SysServerUnavailable
	SysResourceExists                              = zerror.SysResourceExists
	SessionTokenNotMatch                           = zerror.SessionTokenNotMatch
	SessionTokenInvalid                            = zerror.SessionTokenInvalid
	SysDBError                         zerror.Code = 3 // 数据库错误
	SysAoiUnknownAdditionType          zerror.Code = 4
	SysConfNotFound                    zerror.Code = 12
	MarchNotExist                      zerror.Code = 100002
	MarchPathNotFound                  zerror.Code = 100003
	MarchAttackPointNotFound           zerror.Code = 100004
	MarchEncampPointNotFound           zerror.Code = 100005
	BattleReportNotFound               zerror.Code = 100006
	PathObstacle                       zerror.Code = 100007
	MarchTargetInvalid                 zerror.Code = 100008
	MarchMoveTickNormal                zerror.Code = 200000
	MarchMoveTickArrived               zerror.Code = 200001
	MarchMoveTickInterrupted           zerror.Code = 200002
	MarchMoveTickReschedule            zerror.Code = 200003
	CityExists                         zerror.Code = 7100000
	CityNotExists                      zerror.Code = 7100001
	FormationExists                    zerror.Code = 7100100
	FormationNotExists                 zerror.Code = 7100101
	FormationNoGeneral                 zerror.Code = 7100102
	FormationNoTroops                  zerror.Code = 7100103
	FormationGeneralWound              zerror.Code = 7100104
	FormationNoWeapon                  zerror.Code = 7100105
	BunkerExists                       zerror.Code = 7100200
	StandFormationAttackOutRangeTarget zerror.Code = 7100300
	StandMarchInFortressState          zerror.Code = 1200223
	StandMarchFortressInCdState        zerror.Code = 1200222

	TeleportForbidMoveMarch zerror.Code = 1200224

	IpIsInLimit zerror.Code = 1200225

	// gvg
	GvgPassNotOpen  zerror.Code = 8880000 // 关卡未开放
	GvgAllianceNone zerror.Code = 8880100

	// initKingdom
	SysKingdomExist    zerror.Code = 8880001
	SysKingdomNotExist zerror.Code = 8880002
	SysKingdomTopicErr zerror.Code = 8880013

	// kingdom map
	KmapBlockOutOfRange   zerror.Code = 8880003
	KmapInvalidCoordinate zerror.Code = 8880004
	KmapNotAvailableTile  zerror.Code = 8880005
	KmapNotEmptyTile      zerror.Code = 8880006
	KmapZoneNotConnect    zerror.Code = 8880007
	KmapFortNotOccupy     zerror.Code = 8880008
	BornZoneIsFull        zerror.Code = 8880009 // 出生州人数已满
	SysCodeMax            zerror.Code = 8890000 // 占用
	ClientToastKey        zerror.Code = 9999999 // 前端"toast"服务器传多语言key
)
